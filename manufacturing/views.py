from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import WorkOrder, WorkOrderItem, ProductionRecord, CostCalculation
from .serializers import (
    WorkOrderSerializer,
    WorkOrderItemSerializer,
    ProductionRecordSerializer,
    CostCalculationSerializer
)


class WorkOrderViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing work orders
    """
    queryset = WorkOrder.objects.all()
    serializer_class = WorkOrderSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'priority']
    search_fields = ['work_order_number', 'notes']
    ordering_fields = ['work_order_number', 'priority', 'planned_start_date', 'created_at']
    ordering = ['-created_at']

    @action(detail=True, methods=['get'])
    def calculate_costs(self, request, pk=None):
        """Calculate costs for this work order"""
        work_order = self.get_object()
        cost_calc = work_order.calculate_costs()
        
        return Response({
            'work_order': WorkOrderSerializer(work_order).data,
            'cost_calculation': CostCalculationSerializer(cost_calc).data
        })

    @action(detail=True, methods=['get'])
    def material_requirements(self, request, pk=None):
        """Get material requirements for this work order"""
        work_order = self.get_object()
        requirements = work_order.generate_material_requirements()
        
        requirements_data = []
        for component_code, data in requirements.items():
            requirements_data.append({
                'product_code': component_code,
                'product_name': data['product'].name,
                'quantity_required': float(data['quantity']),
                'unit_cost': float(data['product'].standard_cost),
                'total_cost': float(data['quantity'] * data['product'].standard_cost),
                'level': data['level']
            })
        
        return Response({
            'work_order': WorkOrderSerializer(work_order).data,
            'material_requirements': requirements_data
        })

    @action(detail=True, methods=['post'])
    def start_production(self, request, pk=None):
        """Start production for a work order"""
        work_order = self.get_object()
        if work_order.status == 'released':
            work_order.status = 'in_progress'
            work_order.save()
            return Response({'status': 'Production started'})
        return Response(
            {'error': 'Work order must be in released status to start production'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['post'])
    def complete_production(self, request, pk=None):
        """Complete production for a work order"""
        work_order = self.get_object()
        if work_order.status == 'in_progress':
            work_order.status = 'completed'
            work_order.save()
            return Response({'status': 'Production completed'})
        return Response(
            {'error': 'Work order must be in progress to complete'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['post'])
    def create_from_xero_invoice(self, request):
        """Create work order from Xero invoice"""
        from xero_integration.models import XeroInvoice
        
        invoice_id = request.data.get('invoice_id')
        if not invoice_id:
            return Response(
                {'error': 'invoice_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            xero_invoice = XeroInvoice.objects.get(id=invoice_id)
            work_order = WorkOrder.create_from_xero_invoice(xero_invoice)
            
            return Response({
                'work_order': WorkOrderSerializer(work_order).data,
                'message': f'Work order {work_order.work_order_number} created successfully'
            })
        except XeroInvoice.DoesNotExist:
            return Response(
                {'error': 'Xero invoice not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class WorkOrderItemViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing work order items
    """
    queryset = WorkOrderItem.objects.select_related('work_order', 'product').all()
    serializer_class = WorkOrderItemSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['work_order', 'product', 'status']
    ordering_fields = ['work_order__work_order_number', 'product__product_code', 'created_at']
    ordering = ['work_order__work_order_number', 'product__product_code']


class ProductionRecordViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing production records
    """
    queryset = ProductionRecord.objects.select_related(
        'work_order', 'work_order_item', 'routing_step'
    ).all()
    serializer_class = ProductionRecordSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['work_order', 'work_order_item', 'production_date']
    ordering_fields = ['production_date', 'work_order__work_order_number', 'created_at']
    ordering = ['-production_date', '-created_at']


class CostCalculationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing cost calculations
    """
    queryset = CostCalculation.objects.select_related('work_order').all()
    serializer_class = CostCalculationSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['work_order']
    ordering_fields = ['work_order__work_order_number', 'calculated_at']
    ordering = ['-calculated_at']

    @action(detail=False, methods=['post'])
    def calculate_costs(self, request):
        """Calculate costs for a work order"""
        work_order_id = request.data.get('work_order_id')
        if not work_order_id:
            return Response(
                {'error': 'work_order_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            work_order = WorkOrder.objects.get(id=work_order_id)
            cost_calc = work_order.calculate_costs()
            serializer = self.get_serializer(cost_calc)
            return Response(serializer.data)
        except WorkOrder.DoesNotExist:
            return Response(
                {'error': 'Work order not found'},
                status=status.HTTP_404_NOT_FOUND
            )
