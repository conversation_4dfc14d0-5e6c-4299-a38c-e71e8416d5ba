import uuid
from django.db import models
from django.utils import timezone
from decimal import Decimal
from core.models import BaseModel, Product, RoutingStep


class WorkOrder(BaseModel):
    """Work orders for manufacturing"""
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('planned', 'Planned'),
        ('released', 'Released'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    work_order_number = models.CharField(max_length=50, unique=True)
    xero_invoice_id = models.UUIDField(null=True, blank=True)  # Link to Xero invoice
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    planned_start_date = models.DateField(null=True, blank=True)
    planned_completion_date = models.DateField(null=True, blank=True)
    actual_start_date = models.DateField(null=True, blank=True)
    actual_completion_date = models.DateField(null=True, blank=True)
    priority = models.DecimalField(max_digits=3, decimal_places=0, default=5)
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"WO-{self.work_order_number}"
    
    def calculate_costs(self):
        """Calculate all costs for this work order"""
        material_cost = Decimal('0.00')
        labor_cost = Decimal('0.00')
        
        for item in self.items.all():
            # Calculate material cost using BOM explosion
            item_material_cost = item.product.get_bom_cost(item.quantity_required)
            material_cost += item_material_cost
            
            # Calculate labor cost using routing
            item_labor_cost = item.product.get_labor_cost(item.quantity_required)
            labor_cost += item_labor_cost
        
        # Calculate overhead (20% of labor cost as default)
        overhead_cost = labor_cost * Decimal('0.20')
        
        # Create or update cost calculation
        cost_calc, created = CostCalculation.objects.get_or_create(
            work_order=self,
            defaults={
                'material_cost': material_cost,
                'labor_cost': labor_cost,
                'overhead_cost': overhead_cost,
                'margin_percentage': Decimal('25.00')  # Default 25% margin
            }
        )
        
        if not created:
            cost_calc.material_cost = material_cost
            cost_calc.labor_cost = labor_cost
            cost_calc.overhead_cost = overhead_cost
            cost_calc.save()
        
        return cost_calc
    
    def generate_material_requirements(self):
        """Generate material requirements for all items in this work order"""
        requirements = {}
        
        for item in self.items.all():
            explosion = item.product.explode_bom(item.quantity_required)
            for component_code, data in explosion.items():
                if component_code in requirements:
                    requirements[component_code]['quantity'] += data['quantity']
                else:
                    requirements[component_code] = data
        
        return requirements
    
    @classmethod
    def create_from_xero_invoice(cls, xero_invoice):
        """Create work order from Xero invoice"""
        from datetime import datetime, timedelta
        
        # Generate work order number
        wo_number = f"{datetime.now().strftime('%Y%m%d')}-{xero_invoice.invoice_number}"
        
        work_order = cls.objects.create(
            work_order_number=wo_number,
            xero_invoice_id=xero_invoice.id,
            status='planned',
            planned_start_date=timezone.now().date(),
            planned_completion_date=timezone.now().date() + timedelta(days=7),
            notes=f"Generated from Xero invoice {xero_invoice.invoice_number}"
        )
        
        # Create work order items from invoice line items
        for line_item in xero_invoice.line_items.all():
            # Try to match line item description to a product
            # This is a simple implementation - in practice you'd have better matching logic
            try:
                product = Product.objects.filter(
                    name__icontains=line_item.description[:50]
                ).first()
                
                if product:
                    WorkOrderItem.objects.create(
                        work_order=work_order,
                        product=product,
                        quantity_required=line_item.quantity
                    )
            except Exception as e:
                # Log error but continue processing
                pass
        
        # Mark invoice as processed
        xero_invoice.is_processed = True
        xero_invoice.save()
        
        return work_order


class WorkOrderItem(BaseModel):
    """Items to be produced in a work order"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    work_order = models.ForeignKey(WorkOrder, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity_required = models.DecimalField(max_digits=10, decimal_places=4)
    quantity_completed = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    class Meta:
        ordering = ['work_order', 'product']
    
    def __str__(self):
        return f"{self.work_order.work_order_number} - {self.product.product_code}"


class ProductionRecord(BaseModel):
    """Records of production activities"""
    work_order = models.ForeignKey(WorkOrder, on_delete=models.CASCADE, related_name='production_records')
    work_order_item = models.ForeignKey(WorkOrderItem, on_delete=models.CASCADE, related_name='production_records')
    routing_step = models.ForeignKey(RoutingStep, on_delete=models.CASCADE, null=True, blank=True)
    quantity_completed = models.DecimalField(max_digits=10, decimal_places=4)
    actual_time_minutes = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    labor_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    production_date = models.DateField()
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-production_date', '-created_at']
    
    def __str__(self):
        return f"{self.work_order.work_order_number} - {self.production_date}"


class CostCalculation(BaseModel):
    """Cost calculations for work orders"""
    work_order = models.OneToOneField(WorkOrder, on_delete=models.CASCADE, related_name='cost_calculation')
    material_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    labor_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    overhead_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    margin_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    selling_price = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    calculated_at = models.DateTimeField(auto_now=True)
    
    def save(self, *args, **kwargs):
        self.total_cost = self.material_cost + self.labor_cost + self.overhead_cost
        if self.margin_percentage > 0:
            self.selling_price = self.total_cost * (1 + self.margin_percentage / 100)
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"Cost for {self.work_order.work_order_number}: ${self.total_cost}"
