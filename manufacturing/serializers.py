from rest_framework import serializers
from .models import WorkOrder, WorkOrderItem, ProductionRecord, CostCalculation


class WorkOrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkOrder
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class WorkOrderItemSerializer(serializers.ModelSerializer):
    work_order_number = serializers.CharField(source='work_order.work_order_number', read_only=True)
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.product_code', read_only=True)
    
    class Meta:
        model = WorkOrderItem
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class ProductionRecordSerializer(serializers.ModelSerializer):
    work_order_number = serializers.CharField(source='work_order.work_order_number', read_only=True)
    product_name = serializers.Char<PERSON>ield(source='work_order_item.product.name', read_only=True)
    routing_step_description = serializers.CharField(source='routing_step.operation_description', read_only=True)
    
    class Meta:
        model = ProductionRecord
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class CostCalculationSerializer(serializers.ModelSerializer):
    work_order_number = serializers.CharField(source='work_order.work_order_number', read_only=True)
    
    class Meta:
        model = CostCalculation
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at', 'total_cost', 'calculated_at')