# Generated by Django 4.2.21 on 2025-06-01 08:15

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('work_order_number', models.CharField(max_length=50, unique=True)),
                ('xero_invoice_id', models.UUIDField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('planned', 'Planned'), ('released', 'Released'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('planned_start_date', models.DateField(blank=True, null=True)),
                ('planned_completion_date', models.DateField(blank=True, null=True)),
                ('actual_start_date', models.DateField(blank=True, null=True)),
                ('actual_completion_date', models.DateField(blank=True, null=True)),
                ('priority', models.DecimalField(decimal_places=0, default=5, max_digits=3)),
                ('notes', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WorkOrderItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity_required', models.DecimalField(decimal_places=4, max_digits=10)),
                ('quantity_completed', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.product')),
                ('work_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='manufacturing.workorder')),
            ],
            options={
                'ordering': ['work_order', 'product'],
            },
        ),
        migrations.CreateModel(
            name='ProductionRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity_completed', models.DecimalField(decimal_places=4, max_digits=10)),
                ('actual_time_minutes', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('labor_hours', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('production_date', models.DateField()),
                ('notes', models.TextField(blank=True)),
                ('routing_step', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.routingstep')),
                ('work_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_records', to='manufacturing.workorder')),
                ('work_order_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='production_records', to='manufacturing.workorderitem')),
            ],
            options={
                'ordering': ['-production_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CostCalculation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('margin_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('selling_price', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('calculated_at', models.DateTimeField(auto_now=True)),
                ('work_order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='cost_calculation', to='manufacturing.workorder')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
