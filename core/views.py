from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import Product, WorkCenter, BOMItem, RoutingStep, UnitOfMeasure
from .serializers import (
    ProductSerializer,
    WorkCenterSerializer,
    BOMItemSerializer,
    RoutingStepSerializer,
    UnitOfMeasureSerializer
)


class ProductViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing products
    """
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product_type', 'is_manufactured', 'is_purchased', 'is_active']
    search_fields = ['product_code', 'name', 'description']
    ordering_fields = ['product_code', 'name', 'created_at']
    ordering = ['product_code']

    @action(detail=True, methods=['get'])
    def bom_explosion(self, request, pk=None):
        """Get BOM explosion for a product"""
        product = self.get_object()
        quantity = float(request.query_params.get('quantity', 1))
        
        explosion = product.explode_bom(quantity)
        
        # Format the response
        explosion_data = []
        for component_code, data in explosion.items():
            explosion_data.append({
                'product_code': component_code,
                'product_name': data['product'].name,
                'quantity_required': float(data['quantity']),
                'unit_cost': float(data['product'].standard_cost),
                'total_cost': float(data['quantity'] * data['product'].standard_cost),
                'level': data['level']
            })
        
        return Response({
            'product': ProductSerializer(product).data,
            'quantity': quantity,
            'explosion': explosion_data
        })

    @action(detail=True, methods=['get'])
    def cost_breakdown(self, request, pk=None):
        """Get cost breakdown for a product"""
        product = self.get_object()
        quantity = float(request.query_params.get('quantity', 1))
        
        material_cost = product.get_bom_cost(quantity)
        labor_cost = product.get_labor_cost(quantity)
        
        return Response({
            'product': ProductSerializer(product).data,
            'quantity': quantity,
            'material_cost': float(material_cost),
            'labor_cost': float(labor_cost),
            'total_cost': float(material_cost + labor_cost)
        })


class WorkCenterViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing work centers
    """
    queryset = WorkCenter.objects.all()
    serializer_class = WorkCenterSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['work_center_code', 'name', 'description']
    ordering_fields = ['work_center_code', 'name', 'created_at']
    ordering = ['work_center_code']


class BOMItemViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing BOM items
    """
    queryset = BOMItem.objects.select_related('parent_product', 'component_product').all()
    serializer_class = BOMItemSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['parent_product', 'component_product', 'is_active']
    ordering_fields = ['parent_product__product_code', 'component_product__product_code', 'created_at']
    ordering = ['parent_product__product_code', 'component_product__product_code']


class RoutingStepViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing routing steps
    """
    queryset = RoutingStep.objects.select_related('product', 'work_center').all()
    serializer_class = RoutingStepSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['product', 'work_center', 'is_active']
    ordering_fields = ['product__product_code', 'sequence_number', 'created_at']
    ordering = ['product__product_code', 'sequence_number']


class UnitOfMeasureViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing units of measure
    """
    queryset = UnitOfMeasure.objects.all()
    serializer_class = UnitOfMeasureSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['unit_type', 'base_unit', 'is_active']
    search_fields = ['code', 'name', 'description']
    ordering_fields = ['code', 'name', 'unit_type', 'created_at']
    ordering = ['unit_type', 'code']
