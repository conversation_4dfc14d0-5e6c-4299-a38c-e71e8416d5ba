from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from datetime import date, timedelta
import uuid

from core.models import Product, WorkCenter, BOMItem, RoutingStep
from manufacturing.models import WorkOrder, WorkOrderItem, CostCalculation
from inventory.models import InventoryTransaction, InventoryLevel
from xero_integration.models import XeroInvoice, XeroInvoiceLineItem


class Command(BaseCommand):
    help = 'Populate the database with sample data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create Work Centers
        work_centers = [
            {'code': 'WC001', 'name': 'Assembly Line 1', 'capacity': 8.0, 'efficiency': 1.0},
            {'code': 'WC002', 'name': 'Machining Center', 'capacity': 16.0, 'efficiency': 0.9},
            {'code': 'WC003', 'name': 'Quality Control', 'capacity': 8.0, 'efficiency': 1.0},
            {'code': 'WC004', 'name': 'Packaging', 'capacity': 8.0, 'efficiency': 1.1},
        ]
        
        for wc_data in work_centers:
            wc, created = WorkCenter.objects.get_or_create(
                work_center_code=wc_data['code'],
                defaults={
                    'name': wc_data['name'],
                    'capacity_hours_per_day': Decimal(str(wc_data['capacity'])),
                    'efficiency_factor': Decimal(str(wc_data['efficiency']))
                }
            )
            if created:
                self.stdout.write(f'Created work center: {wc.work_center_code}')

        # Create Products
        products_data = [
            # Raw Materials
            {'code': 'RM001', 'name': 'Steel Sheet 1mm', 'type': 'raw_material', 'cost': 25.50, 'manufactured': False, 'purchased': True},
            {'code': 'RM002', 'name': 'Aluminum Rod 10mm', 'type': 'raw_material', 'cost': 15.75, 'manufactured': False, 'purchased': True},
            {'code': 'RM003', 'name': 'Plastic Pellets', 'type': 'raw_material', 'cost': 8.25, 'manufactured': False, 'purchased': True},
            {'code': 'RM004', 'name': 'Screws M6x20', 'type': 'raw_material', 'cost': 0.15, 'manufactured': False, 'purchased': True},
            {'code': 'RM005', 'name': 'Paint - Blue', 'type': 'raw_material', 'cost': 12.50, 'manufactured': False, 'purchased': True},
            
            # Sub-assemblies
            {'code': 'SA001', 'name': 'Base Frame Assembly', 'type': 'manufactured', 'cost': 85.00, 'manufactured': True, 'purchased': False},
            {'code': 'SA002', 'name': 'Motor Mount', 'type': 'manufactured', 'cost': 45.00, 'manufactured': True, 'purchased': False},
            {'code': 'SA003', 'name': 'Control Panel', 'type': 'manufactured', 'cost': 125.00, 'manufactured': True, 'purchased': False},
            
            # Finished Goods
            {'code': 'FG001', 'name': 'Industrial Pump Model A', 'type': 'finished_good', 'cost': 450.00, 'manufactured': True, 'purchased': False},
            {'code': 'FG002', 'name': 'Industrial Pump Model B', 'type': 'finished_good', 'cost': 650.00, 'manufactured': True, 'purchased': False},
        ]
        
        products = {}
        for prod_data in products_data:
            product, created = Product.objects.get_or_create(
                product_code=prod_data['code'],
                defaults={
                    'name': prod_data['name'],
                    'product_type': prod_data['type'],
                    'standard_cost': Decimal(str(prod_data['cost'])),
                    'is_manufactured': prod_data['manufactured'],
                    'is_purchased': prod_data['purchased']
                }
            )
            products[prod_data['code']] = product
            if created:
                self.stdout.write(f'Created product: {product.product_code}')

        # Create BOMs
        bom_data = [
            # Base Frame Assembly BOM
            {'parent': 'SA001', 'component': 'RM001', 'quantity': 2.0, 'scrap': 0.05},
            {'parent': 'SA001', 'component': 'RM002', 'quantity': 4.0, 'scrap': 0.02},
            {'parent': 'SA001', 'component': 'RM004', 'quantity': 8.0, 'scrap': 0.01},
            
            # Motor Mount BOM
            {'parent': 'SA002', 'component': 'RM001', 'quantity': 1.0, 'scrap': 0.03},
            {'parent': 'SA002', 'component': 'RM004', 'quantity': 4.0, 'scrap': 0.01},
            
            # Control Panel BOM
            {'parent': 'SA003', 'component': 'RM003', 'quantity': 0.5, 'scrap': 0.02},
            {'parent': 'SA003', 'component': 'RM004', 'quantity': 6.0, 'scrap': 0.01},
            {'parent': 'SA003', 'component': 'RM005', 'quantity': 0.2, 'scrap': 0.05},
            
            # Finished Good BOMs
            {'parent': 'FG001', 'component': 'SA001', 'quantity': 1.0, 'scrap': 0.0},
            {'parent': 'FG001', 'component': 'SA002', 'quantity': 1.0, 'scrap': 0.0},
            {'parent': 'FG001', 'component': 'SA003', 'quantity': 1.0, 'scrap': 0.0},
            
            {'parent': 'FG002', 'component': 'SA001', 'quantity': 1.0, 'scrap': 0.0},
            {'parent': 'FG002', 'component': 'SA002', 'quantity': 2.0, 'scrap': 0.0},
            {'parent': 'FG002', 'component': 'SA003', 'quantity': 1.0, 'scrap': 0.0},
            {'parent': 'FG002', 'component': 'RM002', 'quantity': 2.0, 'scrap': 0.02},
        ]
        
        for bom in bom_data:
            bom_item, created = BOMItem.objects.get_or_create(
                parent_product=products[bom['parent']],
                component_product=products[bom['component']],
                defaults={
                    'quantity_required': Decimal(str(bom['quantity'])),
                    'scrap_factor': Decimal(str(bom['scrap']))
                }
            )
            if created:
                self.stdout.write(f'Created BOM: {bom["parent"]} -> {bom["component"]}')

        # Create Routing Steps
        routing_data = [
            # Base Frame Assembly Routing
            {'product': 'SA001', 'wc': 'WC002', 'seq': 10, 'desc': 'Cut Steel Sheets', 'setup': 30, 'run': 5.0, 'rate': 25.00},
            {'product': 'SA001', 'wc': 'WC001', 'seq': 20, 'desc': 'Weld Frame', 'setup': 15, 'run': 12.0, 'rate': 30.00},
            {'product': 'SA001', 'wc': 'WC003', 'seq': 30, 'desc': 'Quality Check', 'setup': 5, 'run': 3.0, 'rate': 22.00},
            
            # Motor Mount Routing
            {'product': 'SA002', 'wc': 'WC002', 'seq': 10, 'desc': 'Machine Mount', 'setup': 20, 'run': 8.0, 'rate': 25.00},
            {'product': 'SA002', 'wc': 'WC003', 'seq': 20, 'desc': 'Quality Check', 'setup': 5, 'run': 2.0, 'rate': 22.00},
            
            # Control Panel Routing
            {'product': 'SA003', 'wc': 'WC001', 'seq': 10, 'desc': 'Mold Panel', 'setup': 45, 'run': 6.0, 'rate': 28.00},
            {'product': 'SA003', 'wc': 'WC001', 'seq': 20, 'desc': 'Paint Panel', 'setup': 15, 'run': 4.0, 'rate': 20.00},
            {'product': 'SA003', 'wc': 'WC003', 'seq': 30, 'desc': 'Quality Check', 'setup': 5, 'run': 2.0, 'rate': 22.00},
            
            # Finished Goods Routing
            {'product': 'FG001', 'wc': 'WC001', 'seq': 10, 'desc': 'Final Assembly', 'setup': 30, 'run': 45.0, 'rate': 32.00},
            {'product': 'FG001', 'wc': 'WC003', 'seq': 20, 'desc': 'Final Test', 'setup': 10, 'run': 15.0, 'rate': 35.00},
            {'product': 'FG001', 'wc': 'WC004', 'seq': 30, 'desc': 'Package', 'setup': 5, 'run': 8.0, 'rate': 18.00},
            
            {'product': 'FG002', 'wc': 'WC001', 'seq': 10, 'desc': 'Final Assembly', 'setup': 45, 'run': 60.0, 'rate': 32.00},
            {'product': 'FG002', 'wc': 'WC003', 'seq': 20, 'desc': 'Final Test', 'setup': 15, 'run': 20.0, 'rate': 35.00},
            {'product': 'FG002', 'wc': 'WC004', 'seq': 30, 'desc': 'Package', 'setup': 5, 'run': 10.0, 'rate': 18.00},
        ]
        
        work_centers_dict = {wc.work_center_code: wc for wc in WorkCenter.objects.all()}
        
        for routing in routing_data:
            routing_step, created = RoutingStep.objects.get_or_create(
                product=products[routing['product']],
                sequence_number=routing['seq'],
                defaults={
                    'work_center': work_centers_dict[routing['wc']],
                    'operation_description': routing['desc'],
                    'setup_time_minutes': Decimal(str(routing['setup'])),
                    'run_time_per_unit_minutes': Decimal(str(routing['run'])),
                    'labor_rate_per_hour': Decimal(str(routing['rate']))
                }
            )
            if created:
                self.stdout.write(f'Created routing: {routing["product"]} - {routing["desc"]}')

        # Create Initial Inventory
        inventory_data = [
            {'product': 'RM001', 'quantity': 100.0, 'cost': 25.50},
            {'product': 'RM002', 'quantity': 200.0, 'cost': 15.75},
            {'product': 'RM003', 'quantity': 50.0, 'cost': 8.25},
            {'product': 'RM004', 'quantity': 1000.0, 'cost': 0.15},
            {'product': 'RM005', 'quantity': 20.0, 'cost': 12.50},
        ]
        
        for inv_data in inventory_data:
            # Create inventory transaction
            transaction = InventoryTransaction.objects.create(
                product=products[inv_data['product']],
                transaction_type='receipt',
                quantity=Decimal(str(inv_data['quantity'])),
                unit_cost=Decimal(str(inv_data['cost'])),
                transaction_date=timezone.now().date(),
                notes='Initial inventory setup'
            )
            self.stdout.write(f'Created inventory for: {inv_data["product"]}')

        # Create Sample Xero Invoice
        xero_invoice = XeroInvoice.objects.create(
            xero_invoice_id='INV-001-SAMPLE',
            invoice_number='INV-001',
            contact_name='ABC Manufacturing Ltd',
            total_amount=Decimal('2250.00'),
            invoice_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=30),
            status='authorised',
            currency_code='AUD',
            raw_data={
                'invoice_id': 'INV-001-SAMPLE',
                'invoice_number': 'INV-001',
                'contact': {'name': 'ABC Manufacturing Ltd'},
                'total': 2250.00,
                'status': 'AUTHORISED'
            }
        )
        
        # Create invoice line items
        line_items = [
            {'desc': 'Industrial Pump Model A', 'qty': 2.0, 'unit': 450.00, 'total': 900.00},
            {'desc': 'Industrial Pump Model B', 'qty': 1.0, 'unit': 650.00, 'total': 650.00},
            {'desc': 'Shipping and Handling', 'qty': 1.0, 'unit': 50.00, 'total': 50.00},
        ]
        
        for item_data in line_items:
            XeroInvoiceLineItem.objects.create(
                xero_invoice=xero_invoice,
                description=item_data['desc'],
                quantity=Decimal(str(item_data['qty'])),
                unit_amount=Decimal(str(item_data['unit'])),
                line_amount=Decimal(str(item_data['total']))
            )
        
        self.stdout.write(f'Created sample Xero invoice: {xero_invoice.invoice_number}')
        
        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))
        self.stdout.write('You can now:')
        self.stdout.write('1. View products and their BOMs')
        self.stdout.write('2. Create work orders from the Xero invoice')
        self.stdout.write('3. Calculate costs and material requirements')
        self.stdout.write('4. Track inventory levels and transactions')