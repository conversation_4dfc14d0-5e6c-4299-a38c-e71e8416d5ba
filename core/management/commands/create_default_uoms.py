from django.core.management.base import BaseCommand
from core.models import UnitOfMeasure


class Command(BaseCommand):
    help = 'Create default units of measure'

    def handle(self, *args, **options):
        # Base units
        base_units = [
            {'code': 'EA', 'name': 'Each', 'unit_type': 'quantity', 'description': 'Individual items'},
            {'code': 'M', 'name': 'Meter', 'unit_type': 'length', 'description': 'Base unit for length'},
            {'code': 'KG', 'name': 'Kilogram', 'unit_type': 'weight', 'description': 'Base unit for weight'},
            {'code': 'L', 'name': 'Liter', 'unit_type': 'volume', 'description': 'Base unit for volume'},
            {'code': 'M2', 'name': 'Square Meter', 'unit_type': 'area', 'description': 'Base unit for area'},
            {'code': 'HR', 'name': 'Hour', 'unit_type': 'time', 'description': 'Base unit for time'},
        ]

        created_base_units = {}
        
        for unit_data in base_units:
            unit, created = UnitOfMeasure.objects.get_or_create(
                code=unit_data['code'],
                defaults=unit_data
            )
            created_base_units[unit_data['code']] = unit
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created base unit: {unit.code} - {unit.name}')
                )
            else:
                self.stdout.write(f'Base unit already exists: {unit.code} - {unit.name}')

        # Derived units
        derived_units = [
            # Length units
            {'code': 'MM', 'name': 'Millimeter', 'unit_type': 'length', 'base_unit': 'M', 'conversion_factor': 0.001},
            {'code': 'CM', 'name': 'Centimeter', 'unit_type': 'length', 'base_unit': 'M', 'conversion_factor': 0.01},
            {'code': 'KM', 'name': 'Kilometer', 'unit_type': 'length', 'base_unit': 'M', 'conversion_factor': 1000},
            {'code': 'IN', 'name': 'Inch', 'unit_type': 'length', 'base_unit': 'M', 'conversion_factor': 0.0254},
            {'code': 'FT', 'name': 'Foot', 'unit_type': 'length', 'base_unit': 'M', 'conversion_factor': 0.3048},
            
            # Weight units
            {'code': 'G', 'name': 'Gram', 'unit_type': 'weight', 'base_unit': 'KG', 'conversion_factor': 0.001},
            {'code': 'T', 'name': 'Tonne', 'unit_type': 'weight', 'base_unit': 'KG', 'conversion_factor': 1000},
            {'code': 'LB', 'name': 'Pound', 'unit_type': 'weight', 'base_unit': 'KG', 'conversion_factor': 0.453592},
            {'code': 'OZ', 'name': 'Ounce', 'unit_type': 'weight', 'base_unit': 'KG', 'conversion_factor': 0.0283495},
            
            # Volume units
            {'code': 'ML', 'name': 'Milliliter', 'unit_type': 'volume', 'base_unit': 'L', 'conversion_factor': 0.001},
            {'code': 'GAL', 'name': 'Gallon', 'unit_type': 'volume', 'base_unit': 'L', 'conversion_factor': 3.78541},
            
            # Area units
            {'code': 'CM2', 'name': 'Square Centimeter', 'unit_type': 'area', 'base_unit': 'M2', 'conversion_factor': 0.0001},
            {'code': 'FT2', 'name': 'Square Foot', 'unit_type': 'area', 'base_unit': 'M2', 'conversion_factor': 0.092903},
            
            # Time units
            {'code': 'MIN', 'name': 'Minute', 'unit_type': 'time', 'base_unit': 'HR', 'conversion_factor': 0.0166667},
            {'code': 'SEC', 'name': 'Second', 'unit_type': 'time', 'base_unit': 'HR', 'conversion_factor': 0.000277778},
            {'code': 'DAY', 'name': 'Day', 'unit_type': 'time', 'base_unit': 'HR', 'conversion_factor': 24},
            
            # Quantity units
            {'code': 'DOZ', 'name': 'Dozen', 'unit_type': 'quantity', 'base_unit': 'EA', 'conversion_factor': 12},
            {'code': 'PAIR', 'name': 'Pair', 'unit_type': 'quantity', 'base_unit': 'EA', 'conversion_factor': 2},
            {'code': 'SET', 'name': 'Set', 'unit_type': 'quantity', 'base_unit': 'EA', 'conversion_factor': 1},
        ]

        for unit_data in derived_units:
            base_unit = created_base_units.get(unit_data['base_unit'])
            if base_unit:
                unit_data['base_unit'] = base_unit
                unit, created = UnitOfMeasure.objects.get_or_create(
                    code=unit_data['code'],
                    defaults=unit_data
                )
                if created:
                    self.stdout.write(
                        self.style.SUCCESS(f'Created derived unit: {unit.code} - {unit.name}')
                    )
                else:
                    self.stdout.write(f'Derived unit already exists: {unit.code} - {unit.name}')

        self.stdout.write(
            self.style.SUCCESS('Successfully created default units of measure!')
        )
