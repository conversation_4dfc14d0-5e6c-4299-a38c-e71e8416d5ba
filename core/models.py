import uuid
from django.db import models
from django.contrib.auth.models import User
from decimal import Decimal


class BaseModel(models.Model):
    """Base model with common fields for all models"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        abstract = True


class Product(BaseModel):
    """Product model for manufactured and purchased items"""
    PRODUCT_TYPES = [
        ('manufactured', 'Manufactured'),
        ('purchased', 'Purchased'),
        ('raw_material', 'Raw Material'),
        ('finished_good', 'Finished Good'),
    ]
    
    product_code = models.CharField(max_length=50, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    product_type = models.CharField(max_length=20, choices=PRODUCT_TYPES)
    standard_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    unit_of_measure = models.CharField(max_length=20, default='EA')
    is_manufactured = models.BooleanField(default=False)
    is_purchased = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['product_code']
    
    def __str__(self):
        return f"{self.product_code} - {self.name}"
    
    def get_bom_cost(self, quantity=1):
        """Calculate total material cost for this product based on BOM"""
        total_cost = Decimal('0.00')
        for bom_item in self.bom_items.filter(is_active=True):
            component_cost = bom_item.component_product.standard_cost
            required_qty = bom_item.quantity_required * quantity
            # Apply scrap factor
            required_qty *= (1 + bom_item.scrap_factor)
            total_cost += component_cost * required_qty
        return total_cost
    
    def get_labor_cost(self, quantity=1):
        """Calculate total labor cost for this product based on routing"""
        total_cost = Decimal('0.00')
        for routing_step in self.routing_steps.filter(is_active=True):
            # Setup time is fixed regardless of quantity
            setup_cost = (routing_step.setup_time_minutes / 60) * routing_step.labor_rate_per_hour
            # Run time scales with quantity
            run_cost = (routing_step.run_time_per_unit_minutes * quantity / 60) * routing_step.labor_rate_per_hour
            total_cost += setup_cost + run_cost
        return total_cost
    
    def explode_bom(self, quantity=1, level=0):
        """
        Explode BOM to get all required materials with quantities
        Returns a dictionary with component products and their total required quantities
        """
        explosion = {}
        
        for bom_item in self.bom_items.filter(is_active=True):
            component = bom_item.component_product
            required_qty = bom_item.quantity_required * quantity
            # Apply scrap factor
            required_qty *= (1 + bom_item.scrap_factor)
            
            if component.product_code in explosion:
                explosion[component.product_code]['quantity'] += required_qty
            else:
                explosion[component.product_code] = {
                    'product': component,
                    'quantity': required_qty,
                    'level': level
                }
            
            # If this component also has a BOM, explode it recursively
            if component.bom_items.filter(is_active=True).exists():
                sub_explosion = component.explode_bom(required_qty, level + 1)
                for sub_code, sub_data in sub_explosion.items():
                    if sub_code in explosion:
                        explosion[sub_code]['quantity'] += sub_data['quantity']
                    else:
                        explosion[sub_code] = sub_data
        
        return explosion


class WorkCenter(BaseModel):
    """Work centers where manufacturing operations are performed"""
    work_center_code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    capacity_hours_per_day = models.DecimalField(max_digits=5, decimal_places=2, default=8.0)
    efficiency_factor = models.DecimalField(max_digits=3, decimal_places=2, default=1.0)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['work_center_code']
    
    def __str__(self):
        return f"{self.work_center_code} - {self.name}"


class BOMItem(BaseModel):
    """Bill of Materials items - components required for a product"""
    parent_product = models.ForeignKey(
        Product, 
        on_delete=models.CASCADE, 
        related_name='bom_items'
    )
    component_product = models.ForeignKey(
        Product, 
        on_delete=models.CASCADE, 
        related_name='used_in_boms'
    )
    quantity_required = models.DecimalField(max_digits=10, decimal_places=4)
    unit_of_measure = models.CharField(max_length=20, default='EA')
    scrap_factor = models.DecimalField(max_digits=5, decimal_places=4, default=0.0)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['parent_product', 'component_product']
        ordering = ['parent_product', 'component_product']
    
    def __str__(self):
        return f"{self.parent_product.product_code} -> {self.component_product.product_code}"


class RoutingStep(BaseModel):
    """Manufacturing routing steps for products"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='routing_steps')
    work_center = models.ForeignKey(WorkCenter, on_delete=models.CASCADE)
    sequence_number = models.IntegerField()
    operation_description = models.CharField(max_length=200)
    setup_time_minutes = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    run_time_per_unit_minutes = models.DecimalField(max_digits=8, decimal_places=4, default=0)
    labor_rate_per_hour = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['product', 'sequence_number']
        ordering = ['product', 'sequence_number']
    
    def __str__(self):
        return f"{self.product.product_code} - Step {self.sequence_number}: {self.operation_description}"
