# Generated by Django 4.2.21 on 2025-06-01 14:16

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_update_bom_unique_constraint'),
    ]

    operations = [
        migrations.CreateModel(
            name='CompanySettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company_name', models.CharField(default='Manufacturing ERP System', max_length=200)),
                ('address_line_1', models.Char<PERSON>ield(blank=True, max_length=200)),
                ('address_line_2', models.CharField(blank=True, max_length=200)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state_province', models.CharField(blank=True, max_length=100)),
                ('postal_code', models.Char<PERSON>ield(blank=True, max_length=20)),
                ('country', models.CharField(blank=True, max_length=100)),
                ('phone', models.CharField(blank=True, max_length=50)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('website', models.URLField(blank=True)),
                ('abn', models.CharField(blank=True, help_text='Australian Business Number', max_length=50)),
                ('tax_number', models.CharField(blank=True, help_text='Tax identification number', max_length=50)),
                ('default_tax_rate', models.DecimalField(decimal_places=4, default=0.1, help_text='Default tax rate (e.g., 0.1000 for 10% GST)', max_digits=5)),
                ('currency_code', models.CharField(default='AUD', max_length=3)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Company Settings',
                'verbose_name_plural': 'Company Settings',
            },
        ),
    ]
