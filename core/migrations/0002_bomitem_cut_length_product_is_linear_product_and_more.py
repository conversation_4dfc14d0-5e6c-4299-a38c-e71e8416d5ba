# Generated by Django 4.2.21 on 2025-06-01 11:24

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='bomitem',
            name='cut_length',
            field=models.DecimalField(blank=True, decimal_places=3, help_text='Required cut length for linear products', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='is_linear_product',
            field=models.BooleanField(default=False, help_text='Product sold in standard lengths'),
        ),
        migrations.AddField(
            model_name='product',
            name='purchased_length',
            field=models.DecimalField(blank=True, decimal_places=3, help_text='Standard length this product is purchased in (for linear products)', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='purchased_quantity',
            field=models.DecimalField(decimal_places=3, default=1, help_text='Quantity per purchase unit (e.g., 1 bar = 6.5 meters)', max_digits=10),
        ),
        migrations.CreateModel(
            name='UnitOfMeasure',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('name', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True)),
                ('unit_type', models.CharField(choices=[('quantity', 'Quantity'), ('length', 'Length'), ('weight', 'Weight'), ('volume', 'Volume'), ('area', 'Area'), ('time', 'Time'), ('temperature', 'Temperature'), ('other', 'Other')], default='quantity', max_length=20)),
                ('conversion_factor', models.DecimalField(decimal_places=6, default=1.0, help_text='How many of this unit equals 1 base unit', max_digits=15)),
                ('is_active', models.BooleanField(default=True)),
                ('base_unit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='derived_units', to='core.unitofmeasure')),
            ],
            options={
                'verbose_name': 'Unit of Measure',
                'verbose_name_plural': 'Units of Measure',
                'ordering': ['unit_type', 'code'],
            },
        ),
        migrations.AddField(
            model_name='bomitem',
            name='unit_of_measure_ref',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='core.unitofmeasure'),
        ),
        migrations.AddField(
            model_name='product',
            name='unit_of_measure_ref',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='products', to='core.unitofmeasure'),
        ),
    ]
