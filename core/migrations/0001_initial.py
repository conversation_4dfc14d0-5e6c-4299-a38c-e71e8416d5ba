# Generated by Django 4.2.21 on 2025-06-01 08:15

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product_code', models.CharField(max_length=50, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('product_type', models.CharField(choices=[('manufactured', 'Manufactured'), ('purchased', 'Purchased'), ('raw_material', 'Raw Material'), ('finished_good', 'Finished Good')], max_length=20)),
                ('standard_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('unit_of_measure', models.CharField(default='EA', max_length=20)),
                ('is_manufactured', models.BooleanField(default=False)),
                ('is_purchased', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['product_code'],
            },
        ),
        migrations.CreateModel(
            name='WorkCenter',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('work_center_code', models.CharField(max_length=20, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('capacity_hours_per_day', models.DecimalField(decimal_places=2, default=8.0, max_digits=5)),
                ('efficiency_factor', models.DecimalField(decimal_places=2, default=1.0, max_digits=3)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['work_center_code'],
            },
        ),
        migrations.CreateModel(
            name='RoutingStep',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sequence_number', models.IntegerField()),
                ('operation_description', models.CharField(max_length=200)),
                ('setup_time_minutes', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('run_time_per_unit_minutes', models.DecimalField(decimal_places=4, default=0, max_digits=8)),
                ('labor_rate_per_hour', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('is_active', models.BooleanField(default=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='routing_steps', to='core.product')),
                ('work_center', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.workcenter')),
            ],
            options={
                'ordering': ['product', 'sequence_number'],
                'unique_together': {('product', 'sequence_number')},
            },
        ),
        migrations.CreateModel(
            name='BOMItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity_required', models.DecimalField(decimal_places=4, max_digits=10)),
                ('unit_of_measure', models.CharField(default='EA', max_length=20)),
                ('scrap_factor', models.DecimalField(decimal_places=4, default=0.0, max_digits=5)),
                ('is_active', models.BooleanField(default=True)),
                ('component_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='used_in_boms', to='core.product')),
                ('parent_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bom_items', to='core.product')),
            ],
            options={
                'ordering': ['parent_product', 'component_product'],
                'unique_together': {('parent_product', 'component_product')},
            },
        ),
    ]
