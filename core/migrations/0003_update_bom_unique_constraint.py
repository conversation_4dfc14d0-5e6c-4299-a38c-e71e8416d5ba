# Generated by Django 4.2.21 on 2025-06-01 13:49

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_bomitem_cut_length_product_is_linear_product_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='bomitem',
            options={'ordering': ['parent_product', 'component_product', 'cut_length']},
        ),
        migrations.AlterUniqueTogether(
            name='bomitem',
            unique_together={('parent_product', 'component_product', 'cut_length')},
        ),
    ]
