from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'products', views.ProductViewSet)
router.register(r'work-centers', views.WorkCenterViewSet)
router.register(r'bom-items', views.BOMItemViewSet)
router.register(r'routing-steps', views.RoutingStepViewSet)
router.register(r'units-of-measure', views.UnitOfMeasureViewSet)

urlpatterns = [
    path('', include(router.urls)),
]