from rest_framework import serializers
from .models import Product, WorkCenter, BOMItem, RoutingStep, UnitOfMeasure, CompanySettings


class UnitOfMeasureSerializer(serializers.ModelSerializer):
    base_unit_name = serializers.CharField(source='base_unit.name', read_only=True)
    base_unit_code = serializers.CharField(source='base_unit.code', read_only=True)

    class Meta:
        model = UnitOfMeasure
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class ProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class WorkCenterSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkCenter
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class BOMItemSerializer(serializers.ModelSerializer):
    parent_product_name = serializers.Char<PERSON><PERSON>(source='parent_product.name', read_only=True)
    component_product_name = serializers.CharField(source='component_product.name', read_only=True)
    
    class Meta:
        model = BOMItem
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class RoutingStepSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    work_center_name = serializers.CharField(source='work_center.name', read_only=True)

    class Meta:
        model = RoutingStep
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class CompanySettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanySettings
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')