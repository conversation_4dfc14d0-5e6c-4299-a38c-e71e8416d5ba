from rest_framework import serializers
from .models import Product, WorkCenter, BOMItem, RoutingStep


class ProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class WorkCenterSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkCenter
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class BOMItemSerializer(serializers.ModelSerializer):
    parent_product_name = serializers.CharField(source='parent_product.name', read_only=True)
    component_product_name = serializers.CharField(source='component_product.name', read_only=True)
    
    class Meta:
        model = BOMItem
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class RoutingStepSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    work_center_name = serializers.Char<PERSON>ield(source='work_center.name', read_only=True)
    
    class Meta:
        model = RoutingStep
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')