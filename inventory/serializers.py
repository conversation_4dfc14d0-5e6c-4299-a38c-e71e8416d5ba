from rest_framework import serializers
from .models import InventoryTransaction, InventoryLevel, StockAlert


class InventoryTransactionSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.product_code', read_only=True)
    
    class Meta:
        model = InventoryTransaction
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class InventoryLevelSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.product_code', read_only=True)
    
    class Meta:
        model = InventoryLevel
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at', 'quantity_available')


class StockAlertSerializer(serializers.ModelSerializer):
    product_name = serializers.Char<PERSON>ield(source='product.name', read_only=True)
    product_code = serializers.Char<PERSON>ield(source='product.product_code', read_only=True)
    
    class Meta:
        model = StockAlert
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')