from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from decimal import Decimal
from .models import InventoryTransaction, InventoryLevel, StockAlert
from .serializers import (
    InventoryTransactionSerializer,
    InventoryLevelSerializer,
    StockAlertSerializer
)
from core.models import Product


class InventoryTransactionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing inventory transactions
    """
    queryset = InventoryTransaction.objects.select_related('product').all()
    serializer_class = InventoryTransactionSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product', 'transaction_type', 'reference_type', 'transaction_date']
    search_fields = ['product__product_code', 'product__name', 'notes']
    ordering_fields = ['transaction_date', 'product__product_code', 'created_at']
    ordering = ['-transaction_date', '-created_at']

    @action(detail=False, methods=['post'])
    def create_receipt(self, request):
        """Create inventory receipt transaction"""
        product_id = request.data.get('product_id')
        quantity = request.data.get('quantity')
        unit_cost = request.data.get('unit_cost', 0)
        notes = request.data.get('notes', '')
        
        if not product_id or not quantity:
            return Response(
                {'error': 'product_id and quantity are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            product = Product.objects.get(id=product_id)
            transaction = InventoryTransaction.objects.create(
                product=product,
                transaction_type='receipt',
                quantity=Decimal(str(quantity)),
                unit_cost=Decimal(str(unit_cost)),
                transaction_date=timezone.now().date(),
                notes=notes
            )
            
            serializer = self.get_serializer(transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Product.DoesNotExist:
            return Response(
                {'error': 'Product not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['post'])
    def create_issue(self, request):
        """Create inventory issue transaction"""
        product_id = request.data.get('product_id')
        quantity = request.data.get('quantity')
        reference_type = request.data.get('reference_type')
        reference_id = request.data.get('reference_id')
        notes = request.data.get('notes', '')
        
        if not product_id or not quantity:
            return Response(
                {'error': 'product_id and quantity are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            product = Product.objects.get(id=product_id)
            
            # Check if enough inventory is available
            try:
                inventory_level = InventoryLevel.objects.get(product=product)
                if inventory_level.quantity_available < Decimal(str(quantity)):
                    return Response(
                        {'error': 'Insufficient inventory available'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except InventoryLevel.DoesNotExist:
                return Response(
                    {'error': 'No inventory available for this product'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            transaction = InventoryTransaction.objects.create(
                product=product,
                transaction_type='issue',
                quantity=Decimal(str(quantity)),
                unit_cost=inventory_level.average_cost,
                reference_type=reference_type,
                reference_id=reference_id,
                transaction_date=timezone.now().date(),
                notes=notes
            )
            
            serializer = self.get_serializer(transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Product.DoesNotExist:
            return Response(
                {'error': 'Product not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class InventoryLevelViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing inventory levels
    """
    queryset = InventoryLevel.objects.select_related('product').all()
    serializer_class = InventoryLevelSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product']
    search_fields = ['product__product_code', 'product__name']
    ordering_fields = ['product__product_code', 'quantity_on_hand', 'last_transaction_date']
    ordering = ['product__product_code']

    @action(detail=False, methods=['get'])
    def low_stock_items(self, request):
        """Get items with low stock levels"""
        threshold = Decimal(request.query_params.get('threshold', '10'))
        
        low_stock_items = self.queryset.filter(
            quantity_available__lte=threshold,
            quantity_available__gt=0
        )
        
        serializer = self.get_serializer(low_stock_items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def out_of_stock_items(self, request):
        """Get items that are out of stock"""
        out_of_stock_items = self.queryset.filter(quantity_available__lte=0)
        
        serializer = self.get_serializer(out_of_stock_items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def allocate_inventory(self, request):
        """Allocate inventory for a specific purpose"""
        product_id = request.data.get('product_id')
        quantity = request.data.get('quantity')
        reference_type = request.data.get('reference_type')
        reference_id = request.data.get('reference_id')
        
        if not all([product_id, quantity, reference_type, reference_id]):
            return Response(
                {'error': 'product_id, quantity, reference_type, and reference_id are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            product = Product.objects.get(id=product_id)
            success = InventoryLevel.allocate_inventory(
                product=product,
                quantity=Decimal(str(quantity)),
                reference_type=reference_type,
                reference_id=reference_id
            )
            
            if success:
                return Response({'message': 'Inventory allocated successfully'})
            else:
                return Response(
                    {'error': 'Insufficient inventory available'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Product.DoesNotExist:
            return Response(
                {'error': 'Product not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class StockAlertViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing stock alerts
    """
    queryset = StockAlert.objects.select_related('product').all()
    serializer_class = StockAlertSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product', 'alert_type', 'is_acknowledged']
    search_fields = ['product__product_code', 'product__name']
    ordering_fields = ['product__product_code', 'created_at', 'alert_type']
    ordering = ['-created_at']

    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """Acknowledge a stock alert"""
        alert = self.get_object()
        alert.is_acknowledged = True
        alert.acknowledged_at = timezone.now()
        alert.save()
        
        serializer = self.get_serializer(alert)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def unacknowledged(self, request):
        """Get unacknowledged stock alerts"""
        unacknowledged_alerts = self.queryset.filter(is_acknowledged=False)
        
        serializer = self.get_serializer(unacknowledged_alerts, many=True)
        return Response(serializer.data)
