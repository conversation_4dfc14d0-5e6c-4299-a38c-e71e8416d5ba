import uuid
from django.db import models
from django.utils import timezone
from decimal import Decimal
from core.models import BaseModel, Product


class InventoryTransaction(BaseModel):
    """Inventory transactions for tracking stock movements"""
    TRANSACTION_TYPES = [
        ('receipt', 'Receipt'),
        ('issue', 'Issue'),
        ('adjustment', 'Adjustment'),
        ('transfer', 'Transfer'),
        ('production_receipt', 'Production Receipt'),
        ('production_issue', 'Production Issue'),
    ]
    
    REFERENCE_TYPES = [
        ('work_order', 'Work Order'),
        ('purchase_order', 'Purchase Order'),
        ('sales_order', 'Sales Order'),
        ('adjustment', 'Inventory Adjustment'),
        ('transfer', 'Inventory Transfer'),
    ]
    
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='inventory_transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.DecimalField(max_digits=10, decimal_places=4)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    reference_type = models.CharField(max_length=20, choices=REFERENCE_TYPES, null=True, blank=True)
    reference_id = models.UUIDField(null=True, blank=True)
    transaction_date = models.DateField()
    notes = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-transaction_date', '-created_at']
    
    def __str__(self):
        return f"{self.product.product_code} - {self.transaction_type}: {self.quantity}"
    
    def save(self, *args, **kwargs):
        """Update inventory levels when transaction is saved"""
        super().save(*args, **kwargs)
        self.update_inventory_level()
    
    def update_inventory_level(self):
        """Update the inventory level for this product"""
        inventory_level, created = InventoryLevel.objects.get_or_create(
            product=self.product,
            defaults={
                'quantity_on_hand': Decimal('0.00'),
                'quantity_allocated': Decimal('0.00'),
                'average_cost': Decimal('0.00')
            }
        )
        
        # Recalculate quantity on hand from all transactions
        transactions = InventoryTransaction.objects.filter(product=self.product)
        total_quantity = Decimal('0.00')
        total_value = Decimal('0.00')
        
        for trans in transactions:
            if trans.transaction_type in ['receipt', 'production_receipt', 'adjustment']:
                if trans.quantity > 0:  # Positive adjustment
                    total_quantity += trans.quantity
                    total_value += trans.quantity * trans.unit_cost
                else:  # Negative adjustment
                    total_quantity += trans.quantity  # quantity is already negative
            elif trans.transaction_type in ['issue', 'production_issue']:
                total_quantity -= trans.quantity
        
        inventory_level.quantity_on_hand = total_quantity
        inventory_level.last_transaction_date = self.transaction_date
        
        # Calculate average cost
        if total_quantity > 0 and total_value > 0:
            inventory_level.average_cost = total_value / total_quantity
        
        inventory_level.save()
        
        # Check for stock alerts
        self.check_stock_alerts(inventory_level)
    
    def check_stock_alerts(self, inventory_level):
        """Check if stock alerts need to be created"""
        # Define thresholds (in practice these would be configurable per product)
        low_stock_threshold = Decimal('10.00')
        overstock_threshold = Decimal('1000.00')
        
        current_qty = inventory_level.quantity_available
        
        # Check for low stock
        if current_qty <= 0:
            StockAlert.objects.get_or_create(
                product=self.product,
                alert_type='out_of_stock',
                defaults={
                    'current_quantity': current_qty,
                    'threshold_quantity': Decimal('0.00')
                }
            )
        elif current_qty <= low_stock_threshold:
            StockAlert.objects.get_or_create(
                product=self.product,
                alert_type='low_stock',
                defaults={
                    'current_quantity': current_qty,
                    'threshold_quantity': low_stock_threshold
                }
            )
        
        # Check for overstock
        if current_qty >= overstock_threshold:
            StockAlert.objects.get_or_create(
                product=self.product,
                alert_type='overstock',
                defaults={
                    'current_quantity': current_qty,
                    'threshold_quantity': overstock_threshold
                }
            )


class InventoryLevel(BaseModel):
    """Current inventory levels for products"""
    product = models.OneToOneField(Product, on_delete=models.CASCADE, related_name='inventory_level')
    quantity_on_hand = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    quantity_allocated = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    quantity_available = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    average_cost = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    last_transaction_date = models.DateField(null=True, blank=True)
    
    def save(self, *args, **kwargs):
        self.quantity_available = self.quantity_on_hand - self.quantity_allocated
        super().save(*args, **kwargs)
    
    class Meta:
        ordering = ['product__product_code']
    
    def __str__(self):
        return f"{self.product.product_code}: {self.quantity_on_hand} on hand"
    
    @classmethod
    def allocate_inventory(cls, product, quantity, reference_type=None, reference_id=None):
        """Allocate inventory for a specific purpose (e.g., work order)"""
        inventory_level, created = cls.objects.get_or_create(
            product=product,
            defaults={
                'quantity_on_hand': Decimal('0.00'),
                'quantity_allocated': Decimal('0.00'),
                'average_cost': Decimal('0.00')
            }
        )
        
        if inventory_level.quantity_available >= quantity:
            inventory_level.quantity_allocated += quantity
            inventory_level.save()
            
            # Create allocation transaction record
            InventoryTransaction.objects.create(
                product=product,
                transaction_type='issue',
                quantity=quantity,
                unit_cost=inventory_level.average_cost,
                reference_type=reference_type,
                reference_id=reference_id,
                transaction_date=timezone.now().date(),
                notes=f"Allocated for {reference_type} {reference_id}"
            )
            
            return True
        return False
    
    @classmethod
    def release_allocation(cls, product, quantity, reference_type=None, reference_id=None):
        """Release allocated inventory"""
        try:
            inventory_level = cls.objects.get(product=product)
            inventory_level.quantity_allocated -= quantity
            if inventory_level.quantity_allocated < 0:
                inventory_level.quantity_allocated = Decimal('0.00')
            inventory_level.save()
            return True
        except cls.DoesNotExist:
            return False


class StockAlert(BaseModel):
    """Stock alerts for low inventory levels"""
    ALERT_TYPES = [
        ('low_stock', 'Low Stock'),
        ('out_of_stock', 'Out of Stock'),
        ('overstock', 'Overstock'),
    ]
    
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='stock_alerts')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    current_quantity = models.DecimalField(max_digits=10, decimal_places=4)
    threshold_quantity = models.DecimalField(max_digits=10, decimal_places=4)
    is_acknowledged = models.BooleanField(default=False)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.product.product_code} - {self.alert_type}"
