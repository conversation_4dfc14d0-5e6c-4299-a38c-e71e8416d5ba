# Generated by Django 4.2.21 on 2025-06-01 08:15

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockAlert',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('alert_type', models.CharField(choices=[('low_stock', 'Low Stock'), ('out_of_stock', 'Out of Stock'), ('overstock', 'Overstock')], max_length=20)),
                ('current_quantity', models.DecimalField(decimal_places=4, max_digits=10)),
                ('threshold_quantity', models.DecimalField(decimal_places=4, max_digits=10)),
                ('is_acknowledged', models.BooleanField(default=False)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_alerts', to='core.product')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InventoryTransaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('transaction_type', models.CharField(choices=[('receipt', 'Receipt'), ('issue', 'Issue'), ('adjustment', 'Adjustment'), ('transfer', 'Transfer'), ('production_receipt', 'Production Receipt'), ('production_issue', 'Production Issue')], max_length=20)),
                ('quantity', models.DecimalField(decimal_places=4, max_digits=10)),
                ('unit_cost', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('reference_type', models.CharField(blank=True, choices=[('work_order', 'Work Order'), ('purchase_order', 'Purchase Order'), ('sales_order', 'Sales Order'), ('adjustment', 'Inventory Adjustment'), ('transfer', 'Inventory Transfer')], max_length=20, null=True)),
                ('reference_id', models.UUIDField(blank=True, null=True)),
                ('transaction_date', models.DateField()),
                ('notes', models.TextField(blank=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_transactions', to='core.product')),
            ],
            options={
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InventoryLevel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity_on_hand', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('quantity_allocated', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('quantity_available', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('average_cost', models.DecimalField(decimal_places=4, default=0, max_digits=10)),
                ('last_transaction_date', models.DateField(blank=True, null=True)),
                ('product', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_level', to='core.product')),
            ],
            options={
                'ordering': ['product__product_code'],
            },
        ),
    ]
