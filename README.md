# Manufacturing ERP System MVP

A modern manufacturing ERP system built with Django REST Framework and React TypeScript, designed to integrate with Xero accounting software for streamlined invoice-to-production workflows.

## Architecture Overview

- **Backend**: Django 4.2+ with Django REST Framework
- **Frontend**: React 18+ with TypeScript and Material-UI
- **Database**: PostgreSQL (database: "oms-cta")
- **State Management**: Redux Toolkit with RTK Query
- **API Documentation**: drf-spectacular (OpenAPI 3.0)
- **Task Queue**: Celery with <PERSON><PERSON> (for Xero sync jobs)

## Project Structure

```
manufacturing-erp/
├── backend/                 # Django project settings
├── core/                   # Core models (Product, WorkCenter, BOM, Routing)
├── manufacturing/          # Work orders, production records, cost calculations
├── inventory/             # Inventory transactions, levels, alerts
├── xero_integration/      # Xero API integration
├── frontend/              # React TypeScript application
├── logs/                  # Application logs
├── requirements.txt       # Python dependencies
└── .env                   # Environment configuration
```

## Features

### Core MVP Features
- **Xero Invoice Import**: Sync draft invoices from Xero API
- **Work Order Management**: Convert invoices to manufacturing work orders
- **Bill of Materials (BOM)**: Define product components and quantities
- **Routing Sheets**: Manufacturing process steps and time standards
- **Inventory Tracking**: Real-time stock levels and transactions
- **Cost Calculations**: Material, labor, and overhead cost tracking

### Django Apps
- **core**: Base models for products, work centers, BOMs, and routing
- **manufacturing**: Work orders, production records, and cost calculations
- **inventory**: Inventory transactions, levels, and stock alerts
- **xero_integration**: Xero API integration and invoice management

## Setup Instructions

### Backend Setup

1. **Create and activate virtual environment**:
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**:
   Copy `.env` file and update database credentials:
   ```bash
   # Database Configuration
   DB_NAME=oms-cta
   DB_USER=postgres
   DB_PASSWORD=your_password
   DB_HOST=localhost
   DB_PORT=5432
   ```

4. **Create PostgreSQL database**:
   ```sql
   CREATE DATABASE "oms-cta";
   ```

5. **Run migrations**:
   ```bash
   python manage.py migrate
   ```

6. **Create superuser**:
   ```bash
   python manage.py createsuperuser
   ```

7. **Start development server**:
   ```bash
   python manage.py runserver
   ```

### Frontend Setup

1. **Navigate to frontend directory**:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development server**:
   ```bash
   npm start
   ```

## API Endpoints

### Core APIs
- `/api/core/products/` - Product management
- `/api/core/work-centers/` - Work center management
- `/api/core/bom-items/` - Bill of materials
- `/api/core/routing-steps/` - Manufacturing routing

### Manufacturing APIs
- `/api/manufacturing/work-orders/` - Work order management
- `/api/manufacturing/production-records/` - Production tracking
- `/api/manufacturing/cost-calculations/` - Cost analysis

### Inventory APIs
- `/api/inventory/transactions/` - Inventory transactions
- `/api/inventory/levels/` - Stock levels
- `/api/inventory/alerts/` - Stock alerts

### Xero Integration APIs
- `/api/xero/invoices/` - Xero invoice management
- `/api/xero/sync/` - Trigger Xero synchronization

### API Documentation
- Swagger UI: `http://localhost:8000/api/docs/`
- ReDoc: `http://localhost:8000/api/redoc/`
- OpenAPI Schema: `http://localhost:8000/api/schema/`

## Database Models

### Core Models
- **Product**: Manufactured and purchased items
- **WorkCenter**: Manufacturing work stations
- **BOMItem**: Bill of materials components
- **RoutingStep**: Manufacturing process steps

### Manufacturing Models
- **WorkOrder**: Production orders
- **WorkOrderItem**: Items to be produced
- **ProductionRecord**: Production activity tracking
- **CostCalculation**: Cost analysis and pricing

### Inventory Models
- **InventoryTransaction**: Stock movements
- **InventoryLevel**: Current stock levels
- **StockAlert**: Low stock notifications

### Xero Integration Models
- **XeroInvoice**: Imported Xero invoices
- **XeroInvoiceLineItem**: Invoice line items
- **XeroSyncLog**: Synchronization history
- **XeroConnection**: API connection settings

## Development Workflow

1. **Backend Development**: Add new models, views, and serializers in respective Django apps
2. **Frontend Development**: Create React components following the established structure
3. **API Integration**: Use RTK Query for API calls and state management
4. **Testing**: Run Django tests and React component tests
5. **Documentation**: Update API documentation and README

## Next Steps

1. **Database Setup**: Create PostgreSQL database "oms-cta"
2. **Xero Integration**: Configure Xero API credentials
3. **Business Logic**: Implement cost calculation algorithms
4. **UI Enhancement**: Add data tables, forms, and charts
5. **Testing**: Add comprehensive test coverage
6. **Deployment**: Configure production environment

## Technology Stack Details

### Backend Technologies
- Django 4.2+ - Web framework
- Django REST Framework - API development
- PostgreSQL - Primary database
- Celery - Asynchronous task processing
- Redis - Task queue and caching
- drf-spectacular - API documentation

### Frontend Technologies
- React 18+ - UI framework
- TypeScript - Type safety
- Material-UI (MUI) - Component library
- Redux Toolkit - State management
- RTK Query - API integration
- React Router - Navigation
- React Hook Form - Form handling

This foundation provides a scalable architecture for expanding into a full manufacturing ERP system with advanced planning, scheduling, and analytics capabilities.