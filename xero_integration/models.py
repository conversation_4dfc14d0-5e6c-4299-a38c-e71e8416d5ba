import uuid
from django.db import models
from core.models import BaseModel


class XeroInvoice(BaseModel):
    """Xero invoice data imported from Xero API"""
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('authorised', 'Authorised'),
        ('paid', 'Paid'),
        ('voided', 'Voided'),
    ]
    
    xero_invoice_id = models.CharField(max_length=100, unique=True)
    invoice_number = models.Char<PERSON>ield(max_length=50)
    contact_name = models.Char<PERSON>ield(max_length=200)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    invoice_date = models.DateField()
    due_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    currency_code = models.Char<PERSON>ield(max_length=3, default='AUD')
    raw_data = models.JSONField()  # Store complete Xero invoice data
    is_processed = models.BooleanField(default=False)  # Whether work order was created
    
    class Meta:
        ordering = ['-invoice_date', '-created_at']
    
    def __str__(self):
        return f"{self.invoice_number} - {self.contact_name}"


class XeroInvoiceLineItem(BaseModel):
    """Line items from Xero invoices"""
    xero_invoice = models.ForeignKey(XeroInvoice, on_delete=models.CASCADE, related_name='line_items')
    description = models.TextField()
    quantity = models.DecimalField(max_digits=10, decimal_places=4, default=1)
    unit_amount = models.DecimalField(max_digits=10, decimal_places=4)
    line_amount = models.DecimalField(max_digits=12, decimal_places=2)
    account_code = models.CharField(max_length=20, blank=True)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    class Meta:
        ordering = ['xero_invoice', 'id']
    
    def __str__(self):
        return f"{self.xero_invoice.invoice_number} - {self.description[:50]}"


class XeroSyncLog(BaseModel):
    """Log of Xero synchronization activities"""
    SYNC_TYPES = [
        ('invoices', 'Invoices'),
        ('contacts', 'Contacts'),
        ('items', 'Items'),
    ]
    
    STATUS_CHOICES = [
        ('started', 'Started'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    sync_type = models.CharField(max_length=20, choices=SYNC_TYPES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    records_processed = models.IntegerField(default=0)
    records_created = models.IntegerField(default=0)
    records_updated = models.IntegerField(default=0)
    error_message = models.TextField(blank=True)
    started_at = models.DateTimeField()
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-started_at']
    
    def __str__(self):
        return f"{self.sync_type} sync - {self.status} at {self.started_at}"


class XeroConnection(BaseModel):
    """Xero API connection settings"""
    tenant_id = models.CharField(max_length=100, unique=True)
    tenant_name = models.CharField(max_length=200)
    access_token = models.TextField()
    refresh_token = models.TextField()
    token_expires_at = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    last_sync_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Xero Connection - {self.tenant_name}"
