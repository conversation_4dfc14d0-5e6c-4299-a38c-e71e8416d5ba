from rest_framework import viewsets, filters, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from .models import XeroInvoice, XeroInvoiceLineItem, XeroSyncLog, XeroConnection
from .serializers import (
    XeroInvoiceSerializer,
    XeroInvoiceLineItemSerializer,
    XeroSyncLogSerializer,
    XeroConnectionSerializer
)


class XeroInvoiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Xero invoices
    """
    queryset = XeroInvoice.objects.prefetch_related('line_items').all()
    serializer_class = XeroInvoiceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'is_processed', 'contact_name']
    search_fields = ['invoice_number', 'contact_name', 'xero_invoice_id']
    ordering_fields = ['invoice_date', 'invoice_number', 'total_amount', 'created_at']
    ordering = ['-invoice_date', '-created_at']


class XeroInvoiceLineItemViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Xero invoice line items
    """
    queryset = XeroInvoiceLineItem.objects.select_related('xero_invoice').all()
    serializer_class = XeroInvoiceLineItemSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['xero_invoice', 'account_code']
    search_fields = ['description', 'account_code']
    ordering_fields = ['xero_invoice__invoice_date', 'line_amount', 'created_at']
    ordering = ['xero_invoice__invoice_date', 'id']


class XeroSyncLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing Xero sync logs
    """
    queryset = XeroSyncLog.objects.all()
    serializer_class = XeroSyncLogSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['sync_type', 'status']
    ordering_fields = ['started_at', 'completed_at', 'records_processed']
    ordering = ['-started_at']


class XeroConnectionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Xero connections
    """
    queryset = XeroConnection.objects.all()
    serializer_class = XeroConnectionSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['is_active']
    ordering_fields = ['tenant_name', 'last_sync_at', 'created_at']
    ordering = ['-created_at']


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def sync_invoices(request):
    """
    Trigger Xero invoice synchronization
    """
    # Create sync log entry
    sync_log = XeroSyncLog.objects.create(
        sync_type='invoices',
        status='started',
        started_at=timezone.now()
    )
    
    try:
        # TODO: Implement actual Xero API integration
        # This is a placeholder for the actual sync logic
        
        # For now, just mark as completed
        sync_log.status = 'completed'
        sync_log.completed_at = timezone.now()
        sync_log.records_processed = 0
        sync_log.save()
        
        return Response({
            'message': 'Sync initiated successfully',
            'sync_log_id': sync_log.id
        })
        
    except Exception as e:
        sync_log.status = 'failed'
        sync_log.error_message = str(e)
        sync_log.completed_at = timezone.now()
        sync_log.save()
        
        return Response(
            {'error': f'Sync failed: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
def xero_callback(request):
    """
    Handle Xero OAuth callback
    """
    # TODO: Implement Xero OAuth callback handling
    # This is a placeholder for the actual OAuth flow
    
    return Response({
        'message': 'Xero callback received',
        'code': request.GET.get('code'),
        'state': request.GET.get('state')
    })
