# Generated by Django 4.2.21 on 2025-06-01 08:15

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='XeroConnection',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tenant_id', models.CharField(max_length=100, unique=True)),
                ('tenant_name', models.CharField(max_length=200)),
                ('access_token', models.TextField()),
                ('refresh_token', models.TextField()),
                ('token_expires_at', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('last_sync_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='XeroInvoice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('xero_invoice_id', models.CharField(max_length=100, unique=True)),
                ('invoice_number', models.CharField(max_length=50)),
                ('contact_name', models.CharField(max_length=200)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('invoice_date', models.DateField()),
                ('due_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('submitted', 'Submitted'), ('authorised', 'Authorised'), ('paid', 'Paid'), ('voided', 'Voided')], max_length=20)),
                ('currency_code', models.CharField(default='AUD', max_length=3)),
                ('raw_data', models.JSONField()),
                ('is_processed', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-invoice_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='XeroSyncLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sync_type', models.CharField(choices=[('invoices', 'Invoices'), ('contacts', 'Contacts'), ('items', 'Items')], max_length=20)),
                ('status', models.CharField(choices=[('started', 'Started'), ('completed', 'Completed'), ('failed', 'Failed')], max_length=20)),
                ('records_processed', models.IntegerField(default=0)),
                ('records_created', models.IntegerField(default=0)),
                ('records_updated', models.IntegerField(default=0)),
                ('error_message', models.TextField(blank=True)),
                ('started_at', models.DateTimeField()),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='XeroInvoiceLineItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('description', models.TextField()),
                ('quantity', models.DecimalField(decimal_places=4, default=1, max_digits=10)),
                ('unit_amount', models.DecimalField(decimal_places=4, max_digits=10)),
                ('line_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('account_code', models.CharField(blank=True, max_length=20)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('xero_invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='xero_integration.xeroinvoice')),
            ],
            options={
                'ordering': ['xero_invoice', 'id'],
            },
        ),
    ]
