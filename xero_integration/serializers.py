from rest_framework import serializers
from .models import XeroInvoice, XeroInvoiceLineItem, XeroSyncLog, XeroConnection


class XeroInvoiceLineItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = XeroInvoiceLineItem
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class XeroInvoiceSerializer(serializers.ModelSerializer):
    line_items = XeroInvoiceLineItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = XeroInvoice
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class XeroSyncLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = XeroSyncLog
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')


class XeroConnectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = XeroConnection
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')
        extra_kwargs = {
            'access_token': {'write_only': True},
            'refresh_token': {'write_only': True},
        }