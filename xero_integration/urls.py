from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'invoices', views.XeroInvoiceViewSet)
router.register(r'line-items', views.XeroInvoiceLineItemViewSet)
router.register(r'sync-logs', views.XeroSyncLogViewSet)
router.register(r'connections', views.XeroConnectionViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('sync/', views.sync_invoices, name='sync_invoices'),
    path('auth/callback/', views.xero_callback, name='xero_callback'),
]