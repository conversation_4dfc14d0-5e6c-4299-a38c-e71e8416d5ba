--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4 (Ubuntu 17.4-1.pgdg24.04+2)
-- Dumped by pg_dump version 17.4 (Ubuntu 17.4-1.pgdg24.04+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: postgres
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: sync_info; Type: TABLE; Schema: public; Owner: db_user
--

CREATE TABLE public.sync_info (
    id integer NOT NULL,
    entity_type character varying(50) NOT NULL,
    last_sync_time timestamp with time zone NOT NULL,
    records_synced integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.sync_info OWNER TO db_user;

--
-- Name: sync_info_id_seq; Type: SEQUENCE; Schema: public; Owner: db_user
--

CREATE SEQUENCE public.sync_info_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.sync_info_id_seq OWNER TO db_user;

--
-- Name: sync_info_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: db_user
--

ALTER SEQUENCE public.sync_info_id_seq OWNED BY public.sync_info.id;


--
-- Name: tdk_timefile; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tdk_timefile (
    date character varying(50),
    "time" character varying(50),
    "employee name" character varying(50),
    hours real,
    "job id" character varying(50),
    "job name" character varying(100)
);


ALTER TABLE public.tdk_timefile OWNER TO postgres;

--
-- Name: xero_invoice_line_items; Type: TABLE; Schema: public; Owner: db_user
--

CREATE TABLE public.xero_invoice_line_items (
    line_item_id integer NOT NULL,
    invoice_id character varying(255),
    item_code character varying(255),
    description text,
    quantity numeric(15,2),
    unit_amount numeric(15,2),
    tax_amount numeric(15,2),
    line_amount numeric(15,2),
    account_code character varying(50),
    tax_type character varying(50),
    discount_rate numeric(5,2),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.xero_invoice_line_items OWNER TO db_user;

--
-- Name: xero_invoice_line_items_line_item_id_seq; Type: SEQUENCE; Schema: public; Owner: db_user
--

CREATE SEQUENCE public.xero_invoice_line_items_line_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.xero_invoice_line_items_line_item_id_seq OWNER TO db_user;

--
-- Name: xero_invoice_line_items_line_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: db_user
--

ALTER SEQUENCE public.xero_invoice_line_items_line_item_id_seq OWNED BY public.xero_invoice_line_items.line_item_id;


--
-- Name: xero_invoices; Type: TABLE; Schema: public; Owner: db_user
--

CREATE TABLE public.xero_invoices (
    invoice_id character varying(255) NOT NULL,
    invoice_number character varying(255),
    contact_id character varying(255),
    contact_name character varying(255),
    total numeric(15,2),
    sub_total numeric(15,2),
    total_tax numeric(15,2),
    currency_code character varying(50),
    date date,
    due_date date,
    status character varying(50),
    line_amount_types character varying(50),
    reference character varying(255),
    branding_theme_id character varying(255),
    url character varying(500),
    has_attachments boolean,
    amount_due numeric(15,2),
    amount_paid numeric(15,2),
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    is_active boolean DEFAULT false,
    job_status character varying(50) DEFAULT 'closed'::character varying,
    manual_due_date date,
    due_date_notes text,
    last_status_change timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.xero_invoices OWNER TO db_user;

--
-- Name: COLUMN xero_invoices.is_active; Type: COMMENT; Schema: public; Owner: db_user
--

COMMENT ON COLUMN public.xero_invoices.is_active IS 'Flag indicating if this invoice represents an active job';


--
-- Name: COLUMN xero_invoices.job_status; Type: COMMENT; Schema: public; Owner: db_user
--

COMMENT ON COLUMN public.xero_invoices.job_status IS 'Current status of the job (e.g., closed, active, on_hold, etc.)';


--
-- Name: COLUMN xero_invoices.manual_due_date; Type: COMMENT; Schema: public; Owner: db_user
--

COMMENT ON COLUMN public.xero_invoices.manual_due_date IS 'Manually set due date for the job';


--
-- Name: COLUMN xero_invoices.due_date_notes; Type: COMMENT; Schema: public; Owner: db_user
--

COMMENT ON COLUMN public.xero_invoices.due_date_notes IS 'Notes explaining how the due date was determined';


--
-- Name: COLUMN xero_invoices.last_status_change; Type: COMMENT; Schema: public; Owner: db_user
--

COMMENT ON COLUMN public.xero_invoices.last_status_change IS 'Timestamp of when the job status was last changed';


--
-- Name: xero_items; Type: TABLE; Schema: public; Owner: db_user
--

CREATE TABLE public.xero_items (
    item_id character varying(255) NOT NULL,
    code character varying(50),
    name character varying(255),
    description text,
    purchase_description text,
    purchase_details jsonb,
    sales_details jsonb,
    is_sold boolean,
    is_purchased boolean,
    inventory_asset_account_code character varying(50),
    quantity_on_hand numeric(15,2),
    updated_date_utc timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.xero_items OWNER TO db_user;

--
-- Name: xero_payments; Type: TABLE; Schema: public; Owner: db_user
--

CREATE TABLE public.xero_payments (
    payment_id character varying(255) NOT NULL,
    invoice_id character varying(255),
    account_id character varying(255),
    date date,
    currency_code character varying(50),
    amount numeric(15,2),
    reference character varying(255),
    is_reconciled boolean,
    status character varying(50),
    payment_type character varying(50),
    updated_date_utc timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.xero_payments OWNER TO db_user;

--
-- Name: sync_info id; Type: DEFAULT; Schema: public; Owner: db_user
--

ALTER TABLE ONLY public.sync_info ALTER COLUMN id SET DEFAULT nextval('public.sync_info_id_seq'::regclass);


--
-- Name: xero_invoice_line_items line_item_id; Type: DEFAULT; Schema: public; Owner: db_user
--

ALTER TABLE ONLY public.xero_invoice_line_items ALTER COLUMN line_item_id SET DEFAULT nextval('public.xero_invoice_line_items_line_item_id_seq'::regclass);


--
-- Name: sync_info sync_info_pkey; Type: CONSTRAINT; Schema: public; Owner: db_user
--

ALTER TABLE ONLY public.sync_info
    ADD CONSTRAINT sync_info_pkey PRIMARY KEY (id);


--
-- Name: xero_invoice_line_items xero_invoice_line_items_pkey; Type: CONSTRAINT; Schema: public; Owner: db_user
--

ALTER TABLE ONLY public.xero_invoice_line_items
    ADD CONSTRAINT xero_invoice_line_items_pkey PRIMARY KEY (line_item_id);


--
-- Name: xero_invoices xero_invoices_pkey; Type: CONSTRAINT; Schema: public; Owner: db_user
--

ALTER TABLE ONLY public.xero_invoices
    ADD CONSTRAINT xero_invoices_pkey PRIMARY KEY (invoice_id);


--
-- Name: xero_items xero_items_pkey; Type: CONSTRAINT; Schema: public; Owner: db_user
--

ALTER TABLE ONLY public.xero_items
    ADD CONSTRAINT xero_items_pkey PRIMARY KEY (item_id);


--
-- Name: xero_payments xero_payments_pkey; Type: CONSTRAINT; Schema: public; Owner: db_user
--

ALTER TABLE ONLY public.xero_payments
    ADD CONSTRAINT xero_payments_pkey PRIMARY KEY (payment_id);


--
-- Name: idx_sync_info_entity_type; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_sync_info_entity_type ON public.sync_info USING btree (entity_type);


--
-- Name: idx_sync_info_last_sync_time; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_sync_info_last_sync_time ON public.sync_info USING btree (last_sync_time);


--
-- Name: idx_xero_invoice_line_items_invoice_id; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_invoice_line_items_invoice_id ON public.xero_invoice_line_items USING btree (invoice_id);


--
-- Name: idx_xero_invoice_line_items_item_code; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_invoice_line_items_item_code ON public.xero_invoice_line_items USING btree (item_code);


--
-- Name: idx_xero_invoices_contact_id; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_invoices_contact_id ON public.xero_invoices USING btree (contact_id);


--
-- Name: idx_xero_invoices_date; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_invoices_date ON public.xero_invoices USING btree (date);


--
-- Name: idx_xero_invoices_due_date; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_invoices_due_date ON public.xero_invoices USING btree (due_date);


--
-- Name: idx_xero_invoices_is_active; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_invoices_is_active ON public.xero_invoices USING btree (is_active);


--
-- Name: idx_xero_invoices_job_status; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_invoices_job_status ON public.xero_invoices USING btree (job_status);


--
-- Name: idx_xero_invoices_status; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_invoices_status ON public.xero_invoices USING btree (status);


--
-- Name: idx_xero_items_code; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_items_code ON public.xero_items USING btree (code);


--
-- Name: idx_xero_items_is_purchased; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_items_is_purchased ON public.xero_items USING btree (is_purchased);


--
-- Name: idx_xero_items_is_sold; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_items_is_sold ON public.xero_items USING btree (is_sold);


--
-- Name: idx_xero_items_name; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_items_name ON public.xero_items USING btree (name);


--
-- Name: idx_xero_payments_date; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_payments_date ON public.xero_payments USING btree (date);


--
-- Name: idx_xero_payments_invoice_id; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_payments_invoice_id ON public.xero_payments USING btree (invoice_id);


--
-- Name: idx_xero_payments_status; Type: INDEX; Schema: public; Owner: db_user
--

CREATE INDEX idx_xero_payments_status ON public.xero_payments USING btree (status);


--
-- Name: xero_invoice_line_items fk_invoice_id; Type: FK CONSTRAINT; Schema: public; Owner: db_user
--

ALTER TABLE ONLY public.xero_invoice_line_items
    ADD CONSTRAINT fk_invoice_id FOREIGN KEY (invoice_id) REFERENCES public.xero_invoices(invoice_id) ON UPDATE CASCADE ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;


--
-- Name: xero_payments xero_payments_invoice_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: db_user
--

ALTER TABLE ONLY public.xero_payments
    ADD CONSTRAINT xero_payments_invoice_id_fkey FOREIGN KEY (invoice_id) REFERENCES public.xero_invoices(invoice_id) ON UPDATE CASCADE ON DELETE SET NULL DEFERRABLE INITIALLY DEFERRED;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;
GRANT ALL ON SCHEMA public TO PUBLIC;


--
-- Name: TABLE tdk_timefile; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE public.tdk_timefile TO db_user;


--
-- PostgreSQL database dump complete
--

