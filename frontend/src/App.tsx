import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Provider } from 'react-redux';
import { store } from './store';
import Layout from './components/common/Layout/Layout';
import Dashboard from './pages/Dashboard/Dashboard';
import XeroInvoices from './pages/XeroInvoices/XeroInvoices';
import WorkOrders from './pages/WorkOrders/WorkOrders';
import Products from './pages/Products/Products';
import UnitOfMeasure from './pages/UnitOfMeasure/UnitOfMeasure';
import Inventory from './pages/Inventory/Inventory';
import Reports from './pages/Reports/Reports';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/xero-invoices" element={<XeroInvoices />} />
              <Route path="/work-orders" element={<WorkOrders />} />
              <Route path="/products" element={<Products />} />
              <Route path="/units-of-measure" element={<UnitOfMeasure />} />
              <Route path="/inventory" element={<Inventory />} />
              <Route path="/reports" element={<Reports />} />
            </Routes>
          </Layout>
        </Router>
      </ThemeProvider>
    </Provider>
  );
}

export default App;
