import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  Alert,
  Divider,
  InputAdornment,
} from '@mui/material';
import {
  Save as SaveIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Language as WebsiteIcon,
  Receipt as TaxIcon,
} from '@mui/icons-material';

interface CompanySettings {
  id?: string;
  company_name: string;
  address_line_1: string;
  address_line_2: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
  phone: string;
  email: string;
  website: string;
  abn: string;
  tax_number: string;
  default_tax_rate: string;
  currency_code: string;
}

const Settings: React.FC = () => {
  const [settings, setSettings] = useState<CompanySettings>({
    company_name: 'Manufacturing ERP System',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state_province: '',
    postal_code: '',
    country: '',
    phone: '',
    email: '',
    website: '',
    abn: '',
    tax_number: '',
    default_tax_rate: '0.1000',
    currency_code: 'AUD',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8000/api/core/company-settings/current/');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      } else {
        throw new Error('Failed to fetch settings');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('http://localhost:8000/api/core/company-settings/update_current/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(data);
        setSuccess('Settings saved successfully!');
        
        // Trigger a custom event to notify other components
        window.dispatchEvent(new CustomEvent('companySettingsUpdated', { detail: data }));
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to save settings');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: keyof CompanySettings) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Company Settings
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={loading}
        >
          Save Settings
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* Company Information */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <BusinessIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">Company Information</Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Company Name"
              value={settings.company_name}
              onChange={handleChange('company_name')}
              required
              helperText="This will appear in the header and on reports"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Currency Code"
              value={settings.currency_code}
              onChange={handleChange('currency_code')}
              placeholder="AUD"
              helperText="3-letter currency code (e.g., AUD, USD, EUR)"
            />
          </Grid>

          {/* Address Information */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LocationIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">Address</Typography>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Address Line 1"
              value={settings.address_line_1}
              onChange={handleChange('address_line_1')}
              placeholder="Street address"
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Address Line 2"
              value={settings.address_line_2}
              onChange={handleChange('address_line_2')}
              placeholder="Unit, suite, etc. (optional)"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="City"
              value={settings.city}
              onChange={handleChange('city')}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="State/Province"
              value={settings.state_province}
              onChange={handleChange('state_province')}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Postal Code"
              value={settings.postal_code}
              onChange={handleChange('postal_code')}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Country"
              value={settings.country}
              onChange={handleChange('country')}
            />
          </Grid>

          {/* Contact Information */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">Contact Information</Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Phone"
              value={settings.phone}
              onChange={handleChange('phone')}
              InputProps={{
                startAdornment: <InputAdornment position="start"><PhoneIcon /></InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={settings.email}
              onChange={handleChange('email')}
              InputProps={{
                startAdornment: <InputAdornment position="start"><EmailIcon /></InputAdornment>,
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Website"
              value={settings.website}
              onChange={handleChange('website')}
              placeholder="https://www.example.com"
              InputProps={{
                startAdornment: <InputAdornment position="start"><WebsiteIcon /></InputAdornment>,
              }}
            />
          </Grid>

          {/* Tax Information */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <TaxIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">Tax Information</Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="ABN (Australian Business Number)"
              value={settings.abn}
              onChange={handleChange('abn')}
              placeholder="12 ***********"
              helperText="Australian Business Number"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Tax Number"
              value={settings.tax_number}
              onChange={handleChange('tax_number')}
              helperText="Other tax identification number"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Default Tax Rate"
              type="number"
              value={parseFloat(settings.default_tax_rate) * 100}
              onChange={(e) => setSettings(prev => ({
                ...prev,
                default_tax_rate: (parseFloat(e.target.value) / 100).toFixed(4)
              }))}
              InputProps={{
                endAdornment: <InputAdornment position="end">%</InputAdornment>,
              }}
              inputProps={{ min: 0, max: 100, step: 0.01 }}
              helperText="Default tax rate for calculations (e.g., 10 for 10% GST)"
            />
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default Settings;
