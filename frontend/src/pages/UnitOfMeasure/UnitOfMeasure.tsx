import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  InputAdornment,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { apiService } from '../../services/api';

interface UnitOfMeasure {
  id: string;
  code: string;
  name: string;
  description: string;
  unit_type: string;
  base_unit: string | null;
  conversion_factor: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const UnitOfMeasure: React.FC = () => {
  const [units, setUnits] = useState<UnitOfMeasure[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [editingUnit, setEditingUnit] = useState<UnitOfMeasure | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [unitToDelete, setUnitToDelete] = useState<UnitOfMeasure | null>(null);
  
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    unit_type: 'quantity',
    base_unit: '',
    conversion_factor: 1,
    is_active: true
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchUnits();
  }, []);

  const fetchUnits = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getUnitsOfMeasure();
      setUnits(response);
    } catch (err) {
      setError('Failed to fetch units of measure');
      console.error('Error fetching units:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddUnit = () => {
    setEditingUnit(null);
    setFormData({
      code: '',
      name: '',
      description: '',
      unit_type: 'quantity',
      base_unit: '',
      conversion_factor: 1,
      is_active: true
    });
    setFormErrors({});
    setFormDialogOpen(true);
  };

  const handleEditUnit = (unit: UnitOfMeasure) => {
    setEditingUnit(unit);
    setFormData({
      code: unit.code,
      name: unit.name,
      description: unit.description,
      unit_type: unit.unit_type,
      base_unit: unit.base_unit || '',
      conversion_factor: unit.conversion_factor,
      is_active: unit.is_active
    });
    setFormErrors({});
    setFormDialogOpen(true);
  };

  const handleDeleteUnit = (unit: UnitOfMeasure) => {
    setUnitToDelete(unit);
    setDeleteDialogOpen(true);
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.code.trim()) {
      errors.code = 'Unit code is required';
    }
    
    if (!formData.name.trim()) {
      errors.name = 'Unit name is required';
    }
    
    if (formData.conversion_factor <= 0) {
      errors.conversion_factor = 'Conversion factor must be greater than 0';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleFormSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError(null);

      const submitData = {
        ...formData,
        base_unit: formData.base_unit || null
      };

      if (editingUnit) {
        await apiService.updateUnitOfMeasure(editingUnit.id, submitData);
      } else {
        await apiService.createUnitOfMeasure(submitData);
      }

      setFormDialogOpen(false);
      fetchUnits();
    } catch (err) {
      setError('Failed to save unit of measure');
      console.error('Error saving unit:', err);
    } finally {
      setLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (!unitToDelete) return;

    try {
      setLoading(true);
      await apiService.deleteUnitOfMeasure(unitToDelete.id);
      setUnits(units.filter(u => u.id !== unitToDelete.id));
      setDeleteDialogOpen(false);
      setUnitToDelete(null);
    } catch (err) {
      setError('Failed to delete unit of measure');
      console.error('Error deleting unit:', err);
    } finally {
      setLoading(false);
    }
  };

  const filteredUnits = units.filter(unit =>
    unit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    unit.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getUnitTypeColor = (type: string) => {
    switch (type) {
      case 'length': return 'primary';
      case 'weight': return 'secondary';
      case 'volume': return 'success';
      case 'area': return 'warning';
      case 'time': return 'info';
      default: return 'default';
    }
  };

  const baseUnits = units.filter(u => u.base_unit === null && u.is_active);

  if (loading && units.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Units of Measure
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchUnits}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddUnit}
          >
            Add Unit
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Search Controls */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search units..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Paper>

      {/* Units Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Code</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Base Unit</TableCell>
              <TableCell align="right">Conversion Factor</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredUnits.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  <Typography variant="body1" color="textSecondary" sx={{ py: 4 }}>
                    {units.length === 0 
                      ? "No units of measure found. Add your first unit to get started."
                      : "No units match your search criteria."
                    }
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              filteredUnits.map((unit) => (
                <TableRow key={unit.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {unit.code}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {unit.name}
                    </Typography>
                    {unit.description && (
                      <Typography variant="caption" color="textSecondary" display="block">
                        {unit.description}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={unit.unit_type.toUpperCase()}
                      color={getUnitTypeColor(unit.unit_type) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {unit.base_unit ? (
                      <Typography variant="body2">
                        {units.find(u => u.id === unit.base_unit)?.code || 'Unknown'}
                      </Typography>
                    ) : (
                      <Typography variant="body2" color="textSecondary">
                        Base Unit
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2">
                      {unit.conversion_factor}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={unit.is_active ? 'Active' : 'Inactive'}
                      color={unit.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                      <IconButton
                        size="small"
                        onClick={() => handleEditUnit(unit)}
                        title="Edit Unit"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteUnit(unit)}
                        title="Delete Unit"
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add/Edit Unit Dialog */}
      <Dialog open={formDialogOpen} onClose={() => setFormDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingUnit ? 'Edit Unit of Measure' : 'Add New Unit of Measure'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, pt: 2 }}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                label="Unit Code"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                error={!!formErrors.code}
                helperText={formErrors.code}
                placeholder="e.g., M, KG, L"
                required
                sx={{ flex: 1 }}
              />
              <TextField
                label="Unit Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                error={!!formErrors.name}
                helperText={formErrors.name}
                placeholder="e.g., Meter, Kilogram, Liter"
                required
                sx={{ flex: 2 }}
              />
            </Box>

            <TextField
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Optional description of the unit"
              multiline
              rows={2}
            />

            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControl sx={{ flex: 1 }}>
                <InputLabel>Unit Type</InputLabel>
                <Select
                  value={formData.unit_type}
                  onChange={(e) => setFormData({ ...formData, unit_type: e.target.value })}
                  label="Unit Type"
                >
                  <MenuItem value="quantity">Quantity</MenuItem>
                  <MenuItem value="length">Length</MenuItem>
                  <MenuItem value="weight">Weight</MenuItem>
                  <MenuItem value="volume">Volume</MenuItem>
                  <MenuItem value="area">Area</MenuItem>
                  <MenuItem value="time">Time</MenuItem>
                  <MenuItem value="temperature">Temperature</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>

              <FormControl sx={{ flex: 1 }}>
                <InputLabel>Base Unit</InputLabel>
                <Select
                  value={formData.base_unit}
                  onChange={(e) => setFormData({ ...formData, base_unit: e.target.value })}
                  label="Base Unit"
                >
                  <MenuItem value="">None (This is a base unit)</MenuItem>
                  {baseUnits.map((unit) => (
                    <MenuItem key={unit.id} value={unit.id}>
                      {unit.code} - {unit.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            <TextField
              label="Conversion Factor"
              type="number"
              value={formData.conversion_factor}
              onChange={(e) => setFormData({ ...formData, conversion_factor: parseFloat(e.target.value) || 1 })}
              error={!!formErrors.conversion_factor}
              helperText={formErrors.conversion_factor || "How many of this unit equals 1 base unit"}
              inputProps={{ min: 0, step: 0.001 }}
              disabled={!formData.base_unit}
            />

            <FormControl>
              <InputLabel>Status</InputLabel>
              <Select
                value={formData.is_active ? 'active' : 'inactive'}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.value === 'active' })}
                label="Status"
              >
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFormDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleFormSubmit} variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={20} /> : (editingUnit ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the unit "{unitToDelete?.name}" ({unitToDelete?.code})?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={confirmDelete} color="error" variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UnitOfMeasure;
