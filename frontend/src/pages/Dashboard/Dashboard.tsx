import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Card, 
  CardContent,
  Paper,
  Button,
  List,
  ListItem,
  ListItemText,
  Chip,
  CircularProgress,
  Alert
} from '@mui/material';
import { 
  TrendingUp, 
  Warning, 
  Inventory, 
  Assignment,
  Add,
  Refresh
} from '@mui/icons-material';
import apiService from '../../services/api';

interface DashboardMetrics {
  workOrders: any[];
  xeroInvoices: any[];
  lowStockItems: any[];
  stockAlerts: any[];
  products: any[];
}

const Dashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    workOrders: [],
    xeroInvoices: [],
    lowStockItems: [],
    stockAlerts: [],
    products: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [workOrders, xeroInvoices, lowStockItems, stockAlerts, products] = await Promise.all([
        apiService.getWorkOrders(),
        apiService.getXeroInvoices(),
        apiService.getLowStockItems(),
        apiService.getStockAlerts(),
        apiService.getProducts()
      ]);

      setMetrics({
        workOrders: (workOrders as any)?.results || workOrders || [],
        xeroInvoices: (xeroInvoices as any)?.results || xeroInvoices || [],
        lowStockItems: (lowStockItems as any)?.results || lowStockItems || [],
        stockAlerts: (stockAlerts as any)?.results || stockAlerts || [],
        products: (products as any)?.results || products || []
      });
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleCreateWorkOrderFromInvoice = async (invoiceId: string) => {
    try {
      await apiService.createWorkOrderFromXeroInvoice(invoiceId);
      fetchDashboardData(); // Refresh data
    } catch (err) {
      console.error('Failed to create work order:', err);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Manufacturing ERP Dashboard
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={fetchDashboardData}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {/* Key Metrics Cards */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 3 }}>
        <Box sx={{ flex: '1 1 250px', minWidth: '250px' }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Assignment sx={{ mr: 1, color: 'primary.main' }} />
                <Typography color="textSecondary" gutterBottom>
                  Active Work Orders
                </Typography>
              </Box>
              <Typography variant="h5" component="div">
                {metrics.workOrders.filter(wo => wo.status === 'in_progress' || wo.status === 'released').length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total: {metrics.workOrders.length}
              </Typography>
            </CardContent>
          </Card>
        </Box>
        
        <Box sx={{ flex: '1 1 250px', minWidth: '250px' }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TrendingUp sx={{ mr: 1, color: 'success.main' }} />
                <Typography color="textSecondary" gutterBottom>
                  Pending Invoices
                </Typography>
              </Box>
              <Typography variant="h5" component="div">
                {metrics.xeroInvoices.filter(inv => !inv.is_processed).length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total: {metrics.xeroInvoices.length}
              </Typography>
            </CardContent>
          </Card>
        </Box>
        
        <Box sx={{ flex: '1 1 250px', minWidth: '250px' }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Warning sx={{ mr: 1, color: 'warning.main' }} />
                <Typography color="textSecondary" gutterBottom>
                  Low Stock Items
                </Typography>
              </Box>
              <Typography variant="h5" component="div">
                {metrics.lowStockItems.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Alerts: {metrics.stockAlerts.filter(alert => !alert.is_acknowledged).length}
              </Typography>
            </CardContent>
          </Card>
        </Box>
        
        <Box sx={{ flex: '1 1 250px', minWidth: '250px' }}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Inventory sx={{ mr: 1, color: 'info.main' }} />
                <Typography color="textSecondary" gutterBottom>
                  Total Products
                </Typography>
              </Box>
              <Typography variant="h5" component="div">
                {metrics.products.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Manufactured: {metrics.products.filter(p => p.is_manufactured).length}
              </Typography>
            </CardContent>
          </Card>
        </Box>
      </Box>
      
      {/* Content Sections */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {/* Recent Work Orders */}
        <Box sx={{ flex: '1 1 400px', minWidth: '400px' }}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Recent Work Orders
            </Typography>
            <List dense>
              {metrics.workOrders.slice(0, 5).map((workOrder) => (
                <ListItem key={workOrder.id}>
                  <ListItemText
                    primary={`WO-${workOrder.work_order_number}`}
                    secondary={`Status: ${workOrder.status} | Priority: ${workOrder.priority}`}
                  />
                  <Chip 
                    label={workOrder.status} 
                    size="small" 
                    color={
                      workOrder.status === 'completed' ? 'success' :
                      workOrder.status === 'in_progress' ? 'primary' :
                      workOrder.status === 'released' ? 'warning' : 'default'
                    }
                  />
                </ListItem>
              ))}
              {metrics.workOrders.length === 0 && (
                <ListItem>
                  <ListItemText primary="No work orders found" />
                </ListItem>
              )}
            </List>
          </Paper>
        </Box>
        
        {/* Pending Xero Invoices */}
        <Box sx={{ flex: '1 1 400px', minWidth: '400px' }}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Pending Xero Invoices
            </Typography>
            <List dense>
              {metrics.xeroInvoices.filter(inv => !inv.is_processed).slice(0, 5).map((invoice) => (
                <ListItem key={invoice.id}>
                  <ListItemText
                    primary={`${invoice.invoice_number} - ${invoice.contact_name}`}
                    secondary={`Amount: $${invoice.total_amount} | Date: ${invoice.invoice_date}`}
                  />
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<Add />}
                    onClick={() => handleCreateWorkOrderFromInvoice(invoice.id)}
                  >
                    Create WO
                  </Button>
                </ListItem>
              ))}
              {metrics.xeroInvoices.filter(inv => !inv.is_processed).length === 0 && (
                <ListItem>
                  <ListItemText primary="No pending invoices" />
                </ListItem>
              )}
            </List>
          </Paper>
        </Box>

        {/* Stock Alerts */}
        <Box sx={{ flex: '1 1 400px', minWidth: '400px' }}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Stock Alerts
            </Typography>
            <List dense>
              {metrics.stockAlerts.filter(alert => !alert.is_acknowledged).slice(0, 5).map((alert) => (
                <ListItem key={alert.id}>
                  <ListItemText
                    primary={`${alert.product.product_code} - ${alert.product.name}`}
                    secondary={`${alert.alert_type}: Current: ${alert.current_quantity}, Threshold: ${alert.threshold_quantity}`}
                  />
                  <Chip 
                    label={alert.alert_type} 
                    size="small" 
                    color={
                      alert.alert_type === 'out_of_stock' ? 'error' :
                      alert.alert_type === 'low_stock' ? 'warning' : 'info'
                    }
                  />
                </ListItem>
              ))}
              {metrics.stockAlerts.filter(alert => !alert.is_acknowledged).length === 0 && (
                <ListItem>
                  <ListItemText primary="No active alerts" />
                </ListItem>
              )}
            </List>
          </Paper>
        </Box>

        {/* Low Stock Items */}
        <Box sx={{ flex: '1 1 400px', minWidth: '400px' }}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Low Stock Items
            </Typography>
            <List dense>
              {metrics.lowStockItems.slice(0, 5).map((item) => (
                <ListItem key={item.id}>
                  <ListItemText
                    primary={`${item.product.product_code} - ${item.product.name}`}
                    secondary={`Available: ${item.quantity_available} | On Hand: ${item.quantity_on_hand}`}
                  />
                  <Chip 
                    label={`${item.quantity_available} available`}
                    size="small" 
                    color={item.quantity_available <= 0 ? 'error' : 'warning'}
                  />
                </ListItem>
              ))}
              {metrics.lowStockItems.length === 0 && (
                <ListItem>
                  <ListItemText primary="No low stock items" />
                </ListItem>
              )}
            </List>
          </Paper>
        </Box>
      </Box>
    </Box>
  );
};

export default Dashboard;