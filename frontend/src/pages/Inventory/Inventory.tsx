import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';

const Inventory: React.FC = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Inventory
        </Typography>
        <Button variant="contained" color="primary">
          Record Transaction
        </Button>
      </Box>
      
      <Paper sx={{ p: 3 }}>
        <Typography variant="body1" color="textSecondary">
          No inventory data available. Add products and record transactions to track inventory levels.
        </Typography>
      </Paper>
    </Box>
  );
};

export default Inventory;