import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';

const XeroInvoices: React.FC = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Xero Invoices
        </Typography>
        <Button variant="contained" color="primary">
          Sync from Xero
        </Button>
      </Box>
      
      <Paper sx={{ p: 3 }}>
        <Typography variant="body1" color="textSecondary">
          No Xero invoices found. Click "Sync from Xero" to import invoices from your Xero account.
        </Typography>
      </Paper>
    </Box>
  );
};

export default XeroInvoices;