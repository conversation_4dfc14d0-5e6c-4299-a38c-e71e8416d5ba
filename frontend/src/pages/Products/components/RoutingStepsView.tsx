import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { apiService } from '../../../services/api';

interface Product {
  id: string;
  product_code: string;
  name: string;
}

interface WorkCenter {
  id: string;
  work_center_code: string;
  name: string;
}

interface RoutingStep {
  id: string;
  product: string;
  work_center: string;
  work_center_name: string;
  sequence_number: number;
  operation_description: string;
  setup_time_minutes: number;
  run_time_per_unit_minutes: number;
  labor_rate_per_hour: number;
  is_active: boolean;
}

interface RoutingStepsViewProps {
  product: Product;
  routingSteps: RoutingStep[];
  onRefresh: () => void;
}

const RoutingStepsView: React.FC<RoutingStepsViewProps> = ({ product, routingSteps, onRefresh }) => {
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRoutingStep, setSelectedRoutingStep] = useState<RoutingStep | null>(null);
  const [workCenters, setWorkCenters] = useState<WorkCenter[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    work_center: '',
    sequence_number: 1,
    operation_description: '',
    setup_time_minutes: 0,
    run_time_per_unit_minutes: 0,
    labor_rate_per_hour: 0,
    is_active: true
  });

  React.useEffect(() => {
    fetchWorkCenters();
  }, []);

  const fetchWorkCenters = async () => {
    try {
      const response = await apiService.getWorkCenters();
      setWorkCenters((response as any).results || response);
    } catch (err) {
      console.error('Error fetching work centers:', err);
    }
  };

  const handleAddRoutingStep = () => {
    const nextSequence = routingSteps.length > 0 
      ? Math.max(...routingSteps.map(step => step.sequence_number)) + 10
      : 10;
    
    setFormData({
      work_center: '',
      sequence_number: nextSequence,
      operation_description: '',
      setup_time_minutes: 0,
      run_time_per_unit_minutes: 0,
      labor_rate_per_hour: 0,
      is_active: true
    });
    setAddDialogOpen(true);
  };

  const handleEditRoutingStep = (routingStep: RoutingStep) => {
    setSelectedRoutingStep(routingStep);
    setFormData({
      work_center: routingStep.work_center,
      sequence_number: routingStep.sequence_number,
      operation_description: routingStep.operation_description,
      setup_time_minutes: routingStep.setup_time_minutes,
      run_time_per_unit_minutes: routingStep.run_time_per_unit_minutes,
      labor_rate_per_hour: routingStep.labor_rate_per_hour,
      is_active: routingStep.is_active
    });
    setEditDialogOpen(true);
  };

  const handleDeleteRoutingStep = (routingStep: RoutingStep) => {
    setSelectedRoutingStep(routingStep);
    setDeleteDialogOpen(true);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = {
        product: product.id,
        ...formData
      };

      if (selectedRoutingStep) {
        await apiService.updateRoutingStep(selectedRoutingStep.id, data);
      } else {
        await apiService.createRoutingStep(data);
      }

      setAddDialogOpen(false);
      setEditDialogOpen(false);
      setSelectedRoutingStep(null);
      onRefresh();
    } catch (err) {
      setError('Failed to save routing step');
      console.error('Error saving routing step:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedRoutingStep) return;

    try {
      setLoading(true);
      await apiService.deleteRoutingStep(selectedRoutingStep.id);
      setDeleteDialogOpen(false);
      setSelectedRoutingStep(null);
      onRefresh();
    } catch (err) {
      setError('Failed to delete routing step');
      console.error('Error deleting routing step:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateTotalTime = (step: RoutingStep, quantity: number = 1) => {
    return step.setup_time_minutes + (step.run_time_per_unit_minutes * quantity);
  };

  const calculateTotalCost = (step: RoutingStep, quantity: number = 1) => {
    const totalMinutes = calculateTotalTime(step, quantity);
    return (totalMinutes / 60) * step.labor_rate_per_hour;
  };

  const sortedRoutingSteps = [...routingSteps].sort((a, b) => a.sequence_number - b.sequence_number);

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Routing Steps - {product.product_code}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddRoutingStep}
        >
          Add Routing Step
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {sortedRoutingSteps.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            No routing steps found. Add routing steps to define the manufacturing process for this product.
          </Typography>
        </Paper>
      ) : (
        <>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Sequence</TableCell>
                  <TableCell>Work Center</TableCell>
                  <TableCell>Operation</TableCell>
                  <TableCell>Setup Time (min)</TableCell>
                  <TableCell>Run Time/Unit (min)</TableCell>
                  <TableCell>Labor Rate ($/hr)</TableCell>
                  <TableCell>Total Time (1 unit)</TableCell>
                  <TableCell>Total Cost (1 unit)</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sortedRoutingSteps.map((step) => {
                  const workCenter = workCenters.find(wc => wc.id === step.work_center);
                  const totalTime = calculateTotalTime(step);
                  const totalCost = calculateTotalCost(step);

                  return (
                    <TableRow key={step.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {step.sequence_number}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {workCenter?.work_center_code || 'Unknown'}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {workCenter?.name || step.work_center_name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {step.operation_description}
                        </Typography>
                      </TableCell>
                      <TableCell>{step.setup_time_minutes}</TableCell>
                      <TableCell>{step.run_time_per_unit_minutes}</TableCell>
                      <TableCell>${step.labor_rate_per_hour.toFixed(2)}</TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {totalTime.toFixed(1)} min
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          ${totalCost.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={step.is_active ? 'Active' : 'Inactive'}
                          color={step.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={() => handleEditRoutingStep(step)}
                          title="Edit"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteRoutingStep(step)}
                          title="Delete"
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>

          <Paper sx={{ p: 2, mt: 2, bgcolor: 'grey.50' }}>
            <Typography variant="h6">
              Total Labor Cost (1 unit): ${sortedRoutingSteps.reduce((total, step) => total + calculateTotalCost(step), 0).toFixed(2)}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Total Time (1 unit): {sortedRoutingSteps.reduce((total, step) => total + calculateTotalTime(step), 0).toFixed(1)} minutes
            </Typography>
          </Paper>
        </>
      )}

      {/* Add/Edit Dialog */}
      <Dialog 
        open={addDialogOpen || editDialogOpen} 
        onClose={() => {
          setAddDialogOpen(false);
          setEditDialogOpen(false);
          setSelectedRoutingStep(null);
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {selectedRoutingStep ? 'Edit Routing Step' : 'Add Routing Step'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Work Center</InputLabel>
              <Select
                value={formData.work_center}
                label="Work Center"
                onChange={(e) => setFormData({ ...formData, work_center: e.target.value })}
              >
                {workCenters.map((wc) => (
                  <MenuItem key={wc.id} value={wc.id}>
                    {wc.work_center_code} - {wc.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="Sequence Number"
              type="number"
              value={formData.sequence_number}
              onChange={(e) => setFormData({ ...formData, sequence_number: parseInt(e.target.value) || 0 })}
              inputProps={{ min: 1, step: 1 }}
            />

            <TextField
              fullWidth
              label="Operation Description"
              value={formData.operation_description}
              onChange={(e) => setFormData({ ...formData, operation_description: e.target.value })}
              multiline
              rows={2}
            />

            <TextField
              fullWidth
              label="Setup Time (minutes)"
              type="number"
              value={formData.setup_time_minutes}
              onChange={(e) => setFormData({ ...formData, setup_time_minutes: parseFloat(e.target.value) || 0 })}
              inputProps={{ min: 0, step: 0.1 }}
            />

            <TextField
              fullWidth
              label="Run Time per Unit (minutes)"
              type="number"
              value={formData.run_time_per_unit_minutes}
              onChange={(e) => setFormData({ ...formData, run_time_per_unit_minutes: parseFloat(e.target.value) || 0 })}
              inputProps={{ min: 0, step: 0.01 }}
            />

            <TextField
              fullWidth
              label="Labor Rate per Hour ($)"
              type="number"
              value={formData.labor_rate_per_hour}
              onChange={(e) => setFormData({ ...formData, labor_rate_per_hour: parseFloat(e.target.value) || 0 })}
              inputProps={{ min: 0, step: 0.01 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setAddDialogOpen(false);
            setEditDialogOpen(false);
            setSelectedRoutingStep(null);
          }}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} variant="contained" disabled={loading}>
            {selectedRoutingStep ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this routing step? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDelete} color="error" variant="contained" disabled={loading}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RoutingStepsView;