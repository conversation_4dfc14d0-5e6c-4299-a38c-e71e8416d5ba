import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon
} from '@mui/icons-material';
import { apiService } from '../../../services/api';

interface Product {
  id: string;
  product_code: string;
  name: string;
  description: string;
  product_type: string;
  standard_cost: number;
  unit_of_measure: string;
  is_manufactured: boolean;
  is_purchased: boolean;
  is_active: boolean;
}

interface BOMItem {
  id: string;
  parent_product: string;
  component_product: string;
  component_product_name: string;
  quantity_required: number;
  unit_of_measure: string;
  scrap_factor: number;
  is_active: boolean;
}

interface BOMTreeViewProps {
  product: Product;
  bomItems: BOMItem[];
  onRefresh: () => void;
}

const BOMTreeView: React.FC<BOMTreeViewProps> = ({ product, bomItems, onRefresh }) => {
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedBomItem, setSelectedBomItem] = useState<BOMItem | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    component_product: '',
    quantity_required: 1,
    unit_of_measure: 'EA',
    scrap_factor: 0,
    is_active: true
  });

  React.useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await apiService.getProducts();
      setProducts((response as any).results || response);
    } catch (err) {
      console.error('Error fetching products:', err);
    }
  };

  const handleAddBomItem = () => {
    setFormData({
      component_product: '',
      quantity_required: 1,
      unit_of_measure: 'EA',
      scrap_factor: 0,
      is_active: true
    });
    setAddDialogOpen(true);
  };

  const handleEditBomItem = (bomItem: BOMItem) => {
    setSelectedBomItem(bomItem);
    setFormData({
      component_product: bomItem.component_product,
      quantity_required: bomItem.quantity_required,
      unit_of_measure: bomItem.unit_of_measure,
      scrap_factor: bomItem.scrap_factor,
      is_active: bomItem.is_active
    });
    setEditDialogOpen(true);
  };

  const handleDeleteBomItem = (bomItem: BOMItem) => {
    setSelectedBomItem(bomItem);
    setDeleteDialogOpen(true);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = {
        parent_product: product.id,
        ...formData
      };

      if (selectedBomItem) {
        await apiService.updateBomItem(selectedBomItem.id, data);
      } else {
        await apiService.createBomItem(data);
      }

      setAddDialogOpen(false);
      setEditDialogOpen(false);
      setSelectedBomItem(null);
      onRefresh();
    } catch (err) {
      setError('Failed to save BOM item');
      console.error('Error saving BOM item:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedBomItem) return;

    try {
      setLoading(true);
      await apiService.deleteBomItem(selectedBomItem.id);
      setDeleteDialogOpen(false);
      setSelectedBomItem(null);
      onRefresh();
    } catch (err) {
      setError('Failed to delete BOM item');
      console.error('Error deleting BOM item:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateTotalCost = () => {
    return bomItems.reduce((total, item) => {
      const componentProduct = products.find(p => p.id === item.component_product);
      if (componentProduct) {
        const adjustedQuantity = item.quantity_required * (1 + item.scrap_factor);
        return total + (componentProduct.standard_cost * adjustedQuantity);
      }
      return total;
    }, 0);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Bill of Materials - {product.product_code}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddBomItem}
        >
          Add Component
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {bomItems.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            No BOM items found. Add components to define the bill of materials for this product.
          </Typography>
        </Paper>
      ) : (
        <>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Component</TableCell>
                  <TableCell>Quantity Required</TableCell>
                  <TableCell>UOM</TableCell>
                  <TableCell>Scrap Factor</TableCell>
                  <TableCell>Unit Cost</TableCell>
                  <TableCell>Extended Cost</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bomItems.map((bomItem) => {
                  const componentProduct = products.find(p => p.id === bomItem.component_product);
                  const adjustedQuantity = bomItem.quantity_required * (1 + bomItem.scrap_factor);
                  const extendedCost = componentProduct ? componentProduct.standard_cost * adjustedQuantity : 0;

                  return (
                    <TableRow key={bomItem.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {componentProduct?.product_code || 'Unknown'}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {componentProduct?.name || bomItem.component_product_name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{bomItem.quantity_required}</TableCell>
                      <TableCell>{bomItem.unit_of_measure}</TableCell>
                      <TableCell>{(bomItem.scrap_factor * 100).toFixed(1)}%</TableCell>
                      <TableCell>
                        ${componentProduct?.standard_cost.toFixed(2) || '0.00'}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          ${extendedCost.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={bomItem.is_active ? 'Active' : 'Inactive'}
                          color={bomItem.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={() => handleEditBomItem(bomItem)}
                          title="Edit"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteBomItem(bomItem)}
                          title="Delete"
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>

          <Paper sx={{ p: 2, mt: 2, bgcolor: 'grey.50' }}>
            <Typography variant="h6">
              Total Material Cost: ${calculateTotalCost().toFixed(2)}
            </Typography>
          </Paper>
        </>
      )}

      {/* Add/Edit Dialog */}
      <Dialog 
        open={addDialogOpen || editDialogOpen} 
        onClose={() => {
          setAddDialogOpen(false);
          setEditDialogOpen(false);
          setSelectedBomItem(null);
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {selectedBomItem ? 'Edit BOM Item' : 'Add BOM Item'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Component Product</InputLabel>
              <Select
                value={formData.component_product}
                label="Component Product"
                onChange={(e) => setFormData({ ...formData, component_product: e.target.value })}
              >
                {products
                  .filter(p => p.id !== product.id) // Don't allow self-reference
                  .map((p) => (
                    <MenuItem key={p.id} value={p.id}>
                      {p.product_code} - {p.name}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="Quantity Required"
              type="number"
              value={formData.quantity_required}
              onChange={(e) => setFormData({ ...formData, quantity_required: parseFloat(e.target.value) || 0 })}
              inputProps={{ min: 0, step: 0.0001 }}
            />

            <TextField
              fullWidth
              label="Unit of Measure"
              value={formData.unit_of_measure}
              onChange={(e) => setFormData({ ...formData, unit_of_measure: e.target.value })}
            />

            <TextField
              fullWidth
              label="Scrap Factor (%)"
              type="number"
              value={formData.scrap_factor * 100}
              onChange={(e) => setFormData({ ...formData, scrap_factor: (parseFloat(e.target.value) || 0) / 100 })}
              inputProps={{ min: 0, max: 100, step: 0.1 }}
              helperText="Percentage of additional material needed due to waste/scrap"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setAddDialogOpen(false);
            setEditDialogOpen(false);
            setSelectedBomItem(null);
          }}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} variant="contained" disabled={loading}>
            {selectedBomItem ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this BOM item? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDelete} color="error" variant="contained" disabled={loading}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BOMTreeView;