import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Tabs,
  Tab,
  IconButton,
  Alert,
  Chip,
  Card,
  CardContent
} from '@mui/material';
import {
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { apiService } from '../../../services/api';
import BOMManagement from './BOMManagement';
import RoutingManagement from './RoutingManagement';

interface Product {
  id: string;
  product_code: string;
  name: string;
  description: string;
  product_type: string;
  standard_cost: number;
  unit_of_measure: string;
  is_manufactured: boolean;
  is_purchased: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ProductDetailDialogProps {
  open: boolean;
  onClose: () => void;
  product: Product;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`product-detail-tabpanel-${index}`}
      aria-labelledby={`product-detail-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ProductDetailDialog: React.FC<ProductDetailDialogProps> = ({
  open,
  onClose,
  product
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [bomItems, setBomItems] = useState<any[]>([]);
  const [routingSteps, setRoutingSteps] = useState<any[]>([]);
  const [costData, setCostData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open && product) {
      fetchProductDetails();
    }
  }, [open, product.id]);

  const fetchProductDetails = async () => {
    try {
      setError(null);

      // Fetch BOM items for this product
      const bomResponse = await apiService.getBomItems();
      const productBomItems = (bomResponse as any[]).filter(
        (item: any) => item.parent_product === product.id
      );
      setBomItems(productBomItems);

      // Fetch routing steps for this product
      const routingResponse = await apiService.getRoutingSteps();
      const productRoutingSteps = (routingResponse as any[]).filter(
        (step: any) => step.product === product.id
      );
      setRoutingSteps(productRoutingSteps);

      // Fetch cost breakdown if available
      try {
        const costResponse = await apiService.getProductCostBreakdown(product.id);
        setCostData(costResponse);
      } catch (costErr) {
        // Cost breakdown might not be available, that's okay
        console.log('Cost breakdown not available:', costErr);
      }
    } catch (err) {
      setError('Failed to fetch product details');
      console.error('Error fetching product details:', err);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getProductTypeColor = (type: string) => {
    switch (type) {
      case 'manufactured': return 'primary';
      case 'purchased': return 'secondary';
      case 'raw_material': return 'warning';
      case 'finished_good': return 'success';
      default: return 'default';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {product.product_code} - {product.name}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton onClick={fetchProductDetails} title="Refresh">
              <RefreshIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>
      
      <DialogContent sx={{ p: 0 }}>
        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="product detail tabs">
            <Tab label="General" />
            <Tab label="BOM" />
            <Tab label="Routing" />
            <Tab label="Costs" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          {/* General Product Information */}
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
            <Box sx={{ flex: 1 }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Product Information
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        Product Code
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {product.product_code}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        Name
                      </Typography>
                      <Typography variant="body1">
                        {product.name}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        Description
                      </Typography>
                      <Typography variant="body1">
                        {product.description || 'No description provided'}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        Product Type
                      </Typography>
                      <Chip
                        label={product.product_type.replace('_', ' ').toUpperCase()}
                        color={getProductTypeColor(product.product_type) as any}
                        size="small"
                      />
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Box>
            
            <Box sx={{ flex: 1 }}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Costing & Manufacturing
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        Standard Cost
                      </Typography>
                      <Typography variant="h5" color="primary">
                        ${product.standard_cost.toFixed(2)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        Unit of Measure
                      </Typography>
                      <Typography variant="body1">
                        {product.unit_of_measure}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        Manufacturing Options
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                        {product.is_manufactured && (
                          <Chip label="Manufactured" color="primary" size="small" />
                        )}
                        {product.is_purchased && (
                          <Chip label="Purchased" color="secondary" size="small" />
                        )}
                        <Chip
                          label={product.is_active ? 'Active' : 'Inactive'}
                          color={product.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </Box>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {/* BOM Management */}
          <BOMManagement
            product={product}
            bomItems={bomItems}
            onBomItemsChange={setBomItems}
            onRefresh={fetchProductDetails}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {/* Routing Management */}
          <RoutingManagement
            product={product}
            routingSteps={routingSteps}
            onRoutingStepsChange={setRoutingSteps}
            onRefresh={fetchProductDetails}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          {/* Cost Analysis */}
          <Box sx={{ p: 2 }}>
            <Typography variant="h6">Cost Analysis</Typography>
            <Typography variant="body2" color="textSecondary">
              Cost analysis functionality is temporarily disabled due to Grid component issues.
            </Typography>
          </Box>
        </TabPanel>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductDetailDialog;