import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert
} from '@mui/material';

interface Product {
  id: string;
  product_code: string;
  name: string;
  standard_cost: number;
  unit_of_measure: string;
}

interface BOMItem {
  id: string;
  component_product: string;
  component_product_name: string;
  quantity_required: number;
  scrap_factor: number;
  is_active: boolean;
}

interface RoutingStep {
  id: string;
  work_center: string;
  work_center_name: string;
  sequence_number: number;
  operation_description: string;
  setup_time_minutes: number;
  run_time_per_unit_minutes: number;
  labor_rate_per_hour: number;
  is_active: boolean;
}

interface CostAnalysisViewProps {
  product: Product;
  costData: any;
  bomItems: BOMItem[];
  routingSteps: RoutingStep[];
}

const CostAnalysisView: React.FC<CostAnalysisViewProps> = ({ 
  product, 
  costData, 
  bomItems, 
  routingSteps 
}) => {
  const [quantity, setQuantity] = useState(1);

  // Calculate material costs from BOM
  const calculateMaterialCosts = () => {
    const materialCosts: any[] = [];
    let totalMaterialCost = 0;

    bomItems.forEach(bomItem => {
      if (bomItem.is_active) {
        const adjustedQuantity = bomItem.quantity_required * quantity * (1 + bomItem.scrap_factor);
        const unitCost = product.standard_cost; // This would ideally come from the component product
        const extendedCost = adjustedQuantity * unitCost;
        
        materialCosts.push({
          component: bomItem.component_product_name,
          quantity: bomItem.quantity_required,
          adjustedQuantity,
          unitCost,
          extendedCost,
          scrapFactor: bomItem.scrap_factor
        });
        
        totalMaterialCost += extendedCost;
      }
    });

    return { materialCosts, totalMaterialCost };
  };

  // Calculate labor costs from routing
  const calculateLaborCosts = () => {
    const laborCosts: any[] = [];
    let totalLaborCost = 0;
    let totalTime = 0;

    routingSteps.forEach(step => {
      if (step.is_active) {
        const setupCost = (step.setup_time_minutes / 60) * step.labor_rate_per_hour;
        const runCost = (step.run_time_per_unit_minutes * quantity / 60) * step.labor_rate_per_hour;
        const totalStepCost = setupCost + runCost;
        const totalStepTime = step.setup_time_minutes + (step.run_time_per_unit_minutes * quantity);
        
        laborCosts.push({
          operation: step.operation_description,
          workCenter: step.work_center_name,
          setupTime: step.setup_time_minutes,
          runTime: step.run_time_per_unit_minutes * quantity,
          totalTime: totalStepTime,
          laborRate: step.labor_rate_per_hour,
          setupCost,
          runCost,
          totalCost: totalStepCost
        });
        
        totalLaborCost += totalStepCost;
        totalTime += totalStepTime;
      }
    });

    return { laborCosts, totalLaborCost, totalTime };
  };

  const { materialCosts, totalMaterialCost } = calculateMaterialCosts();
  const { laborCosts, totalLaborCost, totalTime } = calculateLaborCosts();
  const totalCost = totalMaterialCost + totalLaborCost;
  const unitCost = quantity > 0 ? totalCost / quantity : 0;

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Cost Analysis - {product.product_code}
        </Typography>
        <TextField
          label="Quantity"
          type="number"
          value={quantity}
          onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
          inputProps={{ min: 1, step: 1 }}
          sx={{ width: 120 }}
        />
      </Box>

      {/* Cost Summary Cards */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 3 }}>
        <Box sx={{ flex: '1 1 250px', minWidth: '250px' }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Material Cost
              </Typography>
              <Typography variant="h5" color="warning.main">
                ${totalMaterialCost.toFixed(2)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                ${quantity > 0 ? (totalMaterialCost / quantity).toFixed(2) : '0.00'} per unit
              </Typography>
            </CardContent>
          </Card>
        </Box>
        <Box sx={{ flex: '1 1 250px', minWidth: '250px' }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Labor Cost
              </Typography>
              <Typography variant="h5" color="info.main">
                ${totalLaborCost.toFixed(2)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                ${quantity > 0 ? (totalLaborCost / quantity).toFixed(2) : '0.00'} per unit
              </Typography>
            </CardContent>
          </Card>
        </Box>
        <Box sx={{ flex: '1 1 250px', minWidth: '250px' }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Cost
              </Typography>
              <Typography variant="h5" color="primary">
                ${totalCost.toFixed(2)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                ${unitCost.toFixed(2)} per unit
              </Typography>
            </CardContent>
          </Card>
        </Box>
        <Box sx={{ flex: '1 1 250px', minWidth: '250px' }}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Time
              </Typography>
              <Typography variant="h5" color="secondary">
                {totalTime.toFixed(1)} min
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {quantity > 0 ? (totalTime / quantity).toFixed(1) : '0.0'} min per unit
              </Typography>
            </CardContent>
          </Card>
        </Box>
      </Box>

      {/* Cost Breakdown */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {/* Material Costs */}
        <Box sx={{ flex: '1 1 500px', minWidth: '500px' }}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Material Cost Breakdown
            </Typography>
            {materialCosts.length === 0 ? (
              <Alert severity="info">
                No BOM items found. Material costs cannot be calculated.
              </Alert>
            ) : (
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Component</TableCell>
                      <TableCell align="right">Qty Required</TableCell>
                      <TableCell align="right">Adj. Qty</TableCell>
                      <TableCell align="right">Unit Cost</TableCell>
                      <TableCell align="right">Extended</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {materialCosts.map((cost, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography variant="body2">
                            {cost.component}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          {(cost.quantity * quantity).toFixed(2)}
                        </TableCell>
                        <TableCell align="right">
                          {cost.adjustedQuantity.toFixed(2)}
                          {cost.scrapFactor > 0 && (
                            <Typography variant="caption" color="textSecondary" display="block">
                              (+{(cost.scrapFactor * 100).toFixed(1)}% scrap)
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell align="right">
                          ${cost.unitCost.toFixed(2)}
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="medium">
                            ${cost.extendedCost.toFixed(2)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell colSpan={4}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          Total Material Cost
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="subtitle2" fontWeight="bold">
                          ${totalMaterialCost.toFixed(2)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>
        </Box>

        {/* Labor Costs */}
        <Box sx={{ flex: '1 1 500px', minWidth: '500px' }}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Labor Cost Breakdown
            </Typography>
            {laborCosts.length === 0 ? (
              <Alert severity="info">
                No routing steps found. Labor costs cannot be calculated.
              </Alert>
            ) : (
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Operation</TableCell>
                      <TableCell align="right">Setup</TableCell>
                      <TableCell align="right">Run</TableCell>
                      <TableCell align="right">Rate</TableCell>
                      <TableCell align="right">Total Cost</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {laborCosts.map((cost, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography variant="body2">
                            {cost.operation}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {cost.workCenter}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2">
                            {cost.setupTime.toFixed(1)}m
                          </Typography>
                          <Typography variant="caption" color="textSecondary" display="block">
                            ${cost.setupCost.toFixed(2)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2">
                            {cost.runTime.toFixed(1)}m
                          </Typography>
                          <Typography variant="caption" color="textSecondary" display="block">
                            ${cost.runCost.toFixed(2)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          ${cost.laborRate.toFixed(2)}/hr
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="medium">
                            ${cost.totalCost.toFixed(2)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell colSpan={4}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          Total Labor Cost
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="subtitle2" fontWeight="bold">
                          ${totalLaborCost.toFixed(2)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>
        </Box>
      </Box>

      {/* Cost Comparison */}
      <Paper sx={{ p: 2, mt: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>
          Cost Analysis Summary
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
          <Box sx={{ flex: '1 1 200px', minWidth: '200px' }}>
            <Typography variant="body2" color="textSecondary">
              Standard Cost (per unit)
            </Typography>
            <Typography variant="h6">
              ${product.standard_cost.toFixed(2)}
            </Typography>
          </Box>
          <Box sx={{ flex: '1 1 200px', minWidth: '200px' }}>
            <Typography variant="body2" color="textSecondary">
              Calculated Cost (per unit)
            </Typography>
            <Typography variant="h6" color={unitCost > product.standard_cost ? 'error' : 'success'}>
              ${unitCost.toFixed(2)}
            </Typography>
          </Box>
          <Box sx={{ flex: '1 1 200px', minWidth: '200px' }}>
            <Typography variant="body2" color="textSecondary">
              Variance
            </Typography>
            <Typography
              variant="h6"
              color={unitCost > product.standard_cost ? 'error' : 'success'}
            >
              ${(unitCost - product.standard_cost).toFixed(2)}
              {product.standard_cost > 0 && (
                <Typography variant="body2" component="span" sx={{ ml: 1 }}>
                  ({(((unitCost - product.standard_cost) / product.standard_cost) * 100).toFixed(1)}%)
                </Typography>
              )}
            </Typography>
          </Box>
        </Box>
        
        {Math.abs(unitCost - product.standard_cost) > 0.01 && (
          <Alert
            severity={unitCost > product.standard_cost ? 'warning' : 'info'}
            sx={{ mt: 2 }}
          >
            {unitCost > product.standard_cost
              ? 'Calculated cost is higher than standard cost. Consider reviewing BOM or routing efficiency.'
              : 'Calculated cost is lower than standard cost. Standard cost may need updating.'
            }
          </Alert>
        )}
      </Paper>
    </Box>
  );
};

export default CostAnalysisView;