import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { apiService } from '../../../services/api';

interface Product {
  id: string;
  product_code: string;
  name: string;
  description: string;
  product_type: string;
  standard_cost: number | string; // API returns string, but we convert to number
  unit_of_measure: string;
  is_manufactured: boolean;
  is_purchased: boolean;
  is_active: boolean;
}

interface WorkCenter {
  id: string;
  work_center_code: string;
  name: string;
  description: string;
  is_active: boolean;
}

interface RoutingStep {
  id: string;
  product: string;
  work_center: string;
  work_center_name: string;
  sequence_number: number;
  operation_description: string;
  setup_time_minutes: number;
  run_time_per_unit_minutes: number;
  labor_rate_per_hour: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface RoutingManagementProps {
  product: Product;
  routingSteps: RoutingStep[];
  onRoutingStepsChange: (steps: RoutingStep[]) => void;
  onRefresh: () => void;
}

const RoutingManagement: React.FC<RoutingManagementProps> = ({
  product,
  routingSteps,
  onRoutingStepsChange,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editingStep, setEditingStep] = useState<RoutingStep | null>(null);
  const [stepToDelete, setStepToDelete] = useState<RoutingStep | null>(null);
  const [workCenters, setWorkCenters] = useState<WorkCenter[]>([]);
  
  // Form state
  const [formData, setFormData] = useState({
    work_center: '',
    sequence_number: 1,
    operation_description: '',
    setup_time_minutes: 0,
    run_time_per_unit_minutes: 0,
    labor_rate_per_hour: 0,
    is_active: true
  });

  useEffect(() => {
    fetchWorkCenters();
  }, []);

  const fetchWorkCenters = async () => {
    try {
      const centers = await apiService.getWorkCenters();
      setWorkCenters(centers.filter((wc: WorkCenter) => wc.is_active));
    } catch (err) {
      console.error('Error fetching work centers:', err);
    }
  };

  const getNextSequenceNumber = () => {
    if (routingSteps.length === 0) return 10;
    const maxSequence = Math.max(...routingSteps.map(step => step.sequence_number));
    return maxSequence + 10;
  };

  const handleAddStep = () => {
    setEditingStep(null);
    setFormData({
      work_center: '',
      sequence_number: getNextSequenceNumber(),
      operation_description: '',
      setup_time_minutes: 0,
      run_time_per_unit_minutes: 0,
      labor_rate_per_hour: 0,
      is_active: true
    });
    setFormDialogOpen(true);
  };

  const handleEditStep = (step: RoutingStep) => {
    setEditingStep(step);
    setFormData({
      work_center: step.work_center,
      sequence_number: step.sequence_number,
      operation_description: step.operation_description,
      setup_time_minutes: step.setup_time_minutes,
      run_time_per_unit_minutes: step.run_time_per_unit_minutes,
      labor_rate_per_hour: step.labor_rate_per_hour,
      is_active: step.is_active
    });
    setFormDialogOpen(true);
  };

  const handleDeleteStep = (step: RoutingStep) => {
    setStepToDelete(step);
    setDeleteDialogOpen(true);
  };

  const handleFormSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      const submitData = {
        product: product.id,
        ...formData
      };

      if (editingStep) {
        await apiService.updateRoutingStep(editingStep.id, submitData);
      } else {
        await apiService.createRoutingStep(submitData);
      }

      setFormDialogOpen(false);
      onRefresh();
    } catch (err) {
      setError('Failed to save routing step');
      console.error('Error saving routing step:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!stepToDelete) return;

    try {
      setLoading(true);
      setError(null);
      
      await apiService.deleteRoutingStep(stepToDelete.id);
      setDeleteDialogOpen(false);
      setStepToDelete(null);
      onRefresh();
    } catch (err) {
      setError('Failed to delete routing step');
      console.error('Error deleting routing step:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateStepCost = (step: RoutingStep, quantity: number = 1) => {
    const setupTime = Number(step.setup_time_minutes || 0);
    const runTime = Number(step.run_time_per_unit_minutes || 0);
    const laborRate = Number(step.labor_rate_per_hour || 0);

    const setupCost = (setupTime / 60) * laborRate;
    const runCost = (runTime * quantity / 60) * laborRate;
    return setupCost + runCost;
  };

  const calculateTotalLaborCost = (quantity: number = 1) => {
    return routingSteps.reduce((total, step) => {
      return total + calculateStepCost(step, quantity);
    }, 0);
  };

  const sortedSteps = [...routingSteps].sort((a, b) => a.sequence_number - b.sequence_number);

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Manufacturing Routing</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddStep}
            disabled={loading}
          >
            Add Operation
          </Button>
          <IconButton onClick={onRefresh} title="Refresh">
            <RefreshIcon />
          </IconButton>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {routingSteps.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="textSecondary">
            Total Labor Cost (per unit): <strong>${calculateTotalLaborCost(1).toFixed(2)}</strong>
          </Typography>
        </Box>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Seq</TableCell>
              <TableCell>Work Center</TableCell>
              <TableCell>Operation</TableCell>
              <TableCell align="right">Setup (min)</TableCell>
              <TableCell align="right">Run Time/Unit (min)</TableCell>
              <TableCell align="right">Labor Rate ($/hr)</TableCell>
              <TableCell align="right">Cost/Unit</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedSteps.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} align="center">
                  <Typography variant="body2" color="textSecondary">
                    No routing steps defined. Click "Add Operation" to get started.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              sortedSteps.map((step) => (
                <TableRow key={step.id}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {step.sequence_number}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {step.work_center_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {step.operation_description}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">{step.setup_time_minutes}</TableCell>
                  <TableCell align="right">{step.run_time_per_unit_minutes}</TableCell>
                  <TableCell align="right">${Number(step.labor_rate_per_hour || 0).toFixed(2)}</TableCell>
                  <TableCell align="right">${calculateStepCost(step, 1).toFixed(2)}</TableCell>
                  <TableCell>
                    <Chip
                      label={step.is_active ? 'Active' : 'Inactive'}
                      color={step.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="Edit">
                      <IconButton
                        size="small"
                        onClick={() => handleEditStep(step)}
                        disabled={loading}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteStep(step)}
                        disabled={loading}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add/Edit Routing Step Dialog */}
      <Dialog open={formDialogOpen} onClose={() => setFormDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingStep ? 'Edit Routing Step' : 'Add Routing Step'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <TextField
              label="Sequence Number"
              type="number"
              value={formData.sequence_number}
              onChange={(e) => setFormData({ ...formData, sequence_number: parseInt(e.target.value) || 0 })}
              inputProps={{ min: 1, step: 1 }}
              fullWidth
              helperText="Order of operations (e.g., 10, 20, 30...)"
            />

            <FormControl fullWidth>
              <InputLabel>Work Center</InputLabel>
              <Select
                value={formData.work_center}
                onChange={(e) => setFormData({ ...formData, work_center: e.target.value })}
                label="Work Center"
              >
                {workCenters.map((center) => (
                  <MenuItem key={center.id} value={center.id}>
                    {center.work_center_code} - {center.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              label="Operation Description"
              value={formData.operation_description}
              onChange={(e) => setFormData({ ...formData, operation_description: e.target.value })}
              fullWidth
              multiline
              rows={2}
              helperText="Describe what happens in this operation"
            />

            <TextField
              label="Setup Time (minutes)"
              type="number"
              value={formData.setup_time_minutes}
              onChange={(e) => setFormData({ ...formData, setup_time_minutes: parseFloat(e.target.value) || 0 })}
              inputProps={{ min: 0, step: 0.01 }}
              fullWidth
              helperText="One-time setup required regardless of quantity"
            />

            <TextField
              label="Run Time per Unit (minutes)"
              type="number"
              value={formData.run_time_per_unit_minutes}
              onChange={(e) => setFormData({ ...formData, run_time_per_unit_minutes: parseFloat(e.target.value) || 0 })}
              inputProps={{ min: 0, step: 0.0001 }}
              fullWidth
              helperText="Time required per unit produced"
            />

            <TextField
              label="Labor Rate ($/hour)"
              type="number"
              value={formData.labor_rate_per_hour}
              onChange={(e) => setFormData({ ...formData, labor_rate_per_hour: parseFloat(e.target.value) || 0 })}
              inputProps={{ min: 0, step: 0.01 }}
              fullWidth
              helperText="Hourly labor cost for this operation"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFormDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleFormSubmit}
            variant="contained"
            disabled={loading || !formData.work_center || !formData.operation_description}
          >
            {loading ? <CircularProgress size={20} /> : (editingStep ? 'Update' : 'Add')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this routing step?
            {stepToDelete && (
              <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
                <Typography variant="body2">
                  <strong>Sequence:</strong> {stepToDelete.sequence_number}
                </Typography>
                <Typography variant="body2">
                  <strong>Operation:</strong> {stepToDelete.operation_description}
                </Typography>
                <Typography variant="body2">
                  <strong>Work Center:</strong> {stepToDelete.work_center_name}
                </Typography>
              </Box>
            )}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RoutingManagement;
