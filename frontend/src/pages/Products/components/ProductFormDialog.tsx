import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid,
  Alert,
  Box
} from '@mui/material';

interface Product {
  id: string;
  product_code: string;
  name: string;
  description: string;
  product_type: string;
  standard_cost: number;
  unit_of_measure: string;
  is_manufactured: boolean;
  is_purchased: boolean;
  is_active: boolean;
}

interface ProductFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  product?: Product | null;
}

const ProductFormDialog: React.FC<ProductFormDialogProps> = ({
  open,
  onClose,
  onSubmit,
  product
}) => {
  const [formData, setFormData] = useState({
    product_code: '',
    name: '',
    description: '',
    product_type: 'manufactured',
    standard_cost: 0,
    unit_of_measure: 'EA',
    is_manufactured: false,
    is_purchased: true,
    is_active: true
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (product) {
      setFormData({
        product_code: product.product_code,
        name: product.name,
        description: product.description,
        product_type: product.product_type,
        standard_cost: product.standard_cost,
        unit_of_measure: product.unit_of_measure,
        is_manufactured: product.is_manufactured,
        is_purchased: product.is_purchased,
        is_active: product.is_active
      });
    } else {
      setFormData({
        product_code: '',
        name: '',
        description: '',
        product_type: 'manufactured',
        standard_cost: 0,
        unit_of_measure: 'EA',
        is_manufactured: false,
        is_purchased: true,
        is_active: true
      });
    }
    setErrors({});
  }, [product, open]);

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.product_code.trim()) {
      newErrors.product_code = 'Product code is required';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (formData.standard_cost < 0) {
      newErrors.standard_cost = 'Standard cost cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleClose = () => {
    setFormData({
      product_code: '',
      name: '',
      description: '',
      product_type: 'manufactured',
      standard_cost: 0,
      unit_of_measure: 'EA',
      is_manufactured: false,
      is_purchased: true,
      is_active: true
    });
    setErrors({});
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {product ? 'Edit Product' : 'Add New Product'}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="Product Code"
                  value={formData.product_code}
                  onChange={(e) => handleChange('product_code', e.target.value)}
                  error={!!errors.product_code}
                  helperText={errors.product_code}
                  required
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="Product Name"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  error={!!errors.name}
                  helperText={errors.name}
                  required
                />
              </Box>
            </Box>
            
            <Box>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                multiline
                rows={3}
              />
            </Box>
            
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
              <Box sx={{ flex: 1 }}>
                <FormControl fullWidth>
                  <InputLabel>Product Type</InputLabel>
                  <Select
                    value={formData.product_type}
                    label="Product Type"
                    onChange={(e) => handleChange('product_type', e.target.value)}
                  >
                    <MenuItem value="manufactured">Manufactured</MenuItem>
                    <MenuItem value="purchased">Purchased</MenuItem>
                    <MenuItem value="raw_material">Raw Material</MenuItem>
                    <MenuItem value="finished_good">Finished Good</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="Unit of Measure"
                  value={formData.unit_of_measure}
                  onChange={(e) => handleChange('unit_of_measure', e.target.value)}
                />
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="Standard Cost"
                  type="number"
                  value={formData.standard_cost}
                  onChange={(e) => handleChange('standard_cost', parseFloat(e.target.value) || 0)}
                  error={!!errors.standard_cost}
                  helperText={errors.standard_cost}
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_manufactured}
                        onChange={(e) => handleChange('is_manufactured', e.target.checked)}
                      />
                    }
                    label="Is Manufactured"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_purchased}
                        onChange={(e) => handleChange('is_purchased', e.target.checked)}
                      />
                    }
                    label="Is Purchased"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={(e) => handleChange('is_active', e.target.checked)}
                      />
                    }
                    label="Is Active"
                  />
                </Box>
              </Box>
            </Box>
          </Box>
          
          {Object.keys(errors).length > 0 && (
            <Alert severity="error" sx={{ mt: 2 }}>
              Please fix the errors above before submitting.
            </Alert>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {product ? 'Update' : 'Create'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductFormDialog;