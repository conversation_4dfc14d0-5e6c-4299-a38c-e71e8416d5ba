import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Alert,
  Box,
  Tabs,
  Tab
} from '@mui/material';
import { apiService } from '../../../services/api';
import BOMManagement from './BOMManagement';
import RoutingManagement from './RoutingManagement';
import CostAnalysis from './CostAnalysis';

interface Product {
  id: string;
  product_code: string;
  name: string;
  description: string;
  product_type: string;
  standard_cost: number | string; // API returns string, but we convert to number
  unit_of_measure: string;
  is_manufactured: boolean;
  is_purchased: boolean;
  is_active: boolean;
}

interface ProductFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  product?: Product | null;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`product-form-tabpanel-${index}`}
      aria-labelledby={`product-form-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ProductFormDialog: React.FC<ProductFormDialogProps> = ({
  open,
  onClose,
  onSubmit,
  product
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState({
    product_code: '',
    name: '',
    description: '',
    product_type: 'manufactured',
    standard_cost: 0,
    unit_of_measure: 'EA',
    is_manufactured: false,
    is_purchased: true,
    is_active: true
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [bomItems, setBomItems] = useState<any[]>([]);
  const [routingSteps, setRoutingSteps] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (product) {
      setFormData({
        product_code: product.product_code,
        name: product.name,
        description: product.description,
        product_type: product.product_type,
        standard_cost: parseFloat(product.standard_cost.toString()) || 0,
        unit_of_measure: product.unit_of_measure,
        is_manufactured: product.is_manufactured,
        is_purchased: product.is_purchased,
        is_active: product.is_active
      });
      fetchProductDetails();
    } else {
      setFormData({
        product_code: '',
        name: '',
        description: '',
        product_type: 'manufactured',
        standard_cost: 0,
        unit_of_measure: 'EA',
        is_manufactured: false,
        is_purchased: true,
        is_active: true
      });
      setBomItems([]);
      setRoutingSteps([]);
    }
    setErrors({});
    setError(null);
    setTabValue(0);
  }, [product, open]);

  const fetchProductDetails = async () => {
    if (!product) return;

    try {
      setError(null);

      // Fetch BOM items for this product
      const bomResponse = await apiService.getBomItems();
      const productBomItems = (bomResponse as any[]).filter(
        (item: any) => item.parent_product === product.id
      );
      setBomItems(productBomItems);

      // Fetch routing steps for this product
      const routingResponse = await apiService.getRoutingSteps();
      const productRoutingSteps = (routingResponse as any[]).filter(
        (step: any) => step.product === product.id
      );
      setRoutingSteps(productRoutingSteps);
    } catch (err) {
      setError('Failed to fetch product details');
      console.error('Error fetching product details:', err);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.product_code.trim()) {
      newErrors.product_code = 'Product code is required';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (formData.standard_cost < 0) {
      newErrors.standard_cost = 'Standard cost cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleClose = () => {
    setFormData({
      product_code: '',
      name: '',
      description: '',
      product_type: 'manufactured',
      standard_cost: 0,
      unit_of_measure: 'EA',
      is_manufactured: false,
      is_purchased: true,
      is_active: true
    });
    setErrors({});
    setError(null);
    setBomItems([]);
    setRoutingSteps([]);
    setTabValue(0);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        {product ? 'Edit Product' : 'Add New Product'}
      </DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="product form tabs">
            <Tab label="General" />
            <Tab label="BOM" disabled={!product} />
            <Tab label="Routing" disabled={!product} />
            <Tab label="Cost" disabled={!product} />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="Product Code"
                  value={formData.product_code}
                  onChange={(e) => handleChange('product_code', e.target.value)}
                  error={!!errors.product_code}
                  helperText={errors.product_code}
                  required
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="Product Name"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  error={!!errors.name}
                  helperText={errors.name}
                  required
                />
              </Box>
            </Box>
            
            <Box>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                multiline
                rows={3}
              />
            </Box>
            
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
              <Box sx={{ flex: 1 }}>
                <FormControl fullWidth>
                  <InputLabel>Product Type</InputLabel>
                  <Select
                    value={formData.product_type}
                    label="Product Type"
                    onChange={(e) => handleChange('product_type', e.target.value)}
                  >
                    <MenuItem value="manufactured">Manufactured</MenuItem>
                    <MenuItem value="purchased">Purchased</MenuItem>
                    <MenuItem value="raw_material">Raw Material</MenuItem>
                    <MenuItem value="finished_good">Finished Good</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="Unit of Measure"
                  value={formData.unit_of_measure}
                  onChange={(e) => handleChange('unit_of_measure', e.target.value)}
                />
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
              <Box sx={{ flex: 1 }}>
                <TextField
                  fullWidth
                  label="Standard Cost"
                  type="number"
                  value={formData.standard_cost}
                  onChange={(e) => handleChange('standard_cost', parseFloat(e.target.value) || 0)}
                  error={!!errors.standard_cost}
                  helperText={errors.standard_cost}
                  inputProps={{ min: 0, step: 0.01 }}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_manufactured}
                        onChange={(e) => handleChange('is_manufactured', e.target.checked)}
                      />
                    }
                    label="Is Manufactured"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_purchased}
                        onChange={(e) => handleChange('is_purchased', e.target.checked)}
                      />
                    }
                    label="Is Purchased"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={(e) => handleChange('is_active', e.target.checked)}
                      />
                    }
                    label="Is Active"
                  />
                </Box>
              </Box>
            </Box>
          </Box>

          {Object.keys(errors).length > 0 && (
            <Alert severity="error" sx={{ mt: 2 }}>
              Please fix the errors above before submitting.
            </Alert>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {/* BOM Management */}
          {product && (
            <BOMManagement
              product={product}
              bomItems={bomItems}
              onBomItemsChange={setBomItems}
              onRefresh={fetchProductDetails}
            />
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {/* Routing Management */}
          {product && (
            <RoutingManagement
              product={product}
              routingSteps={routingSteps}
              onRoutingStepsChange={setRoutingSteps}
              onRefresh={fetchProductDetails}
            />
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          {/* Cost Analysis */}
          {product && (
            <CostAnalysis
              product={product}
              costData={null}
              onRefresh={fetchProductDetails}
            />
          )}
        </TabPanel>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {product ? 'Update' : 'Create'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductFormDialog;