import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Straighten as LinearIcon
} from '@mui/icons-material';
import { apiService } from '../../../services/api';

interface UnitOfMeasure {
  id: string;
  code: string;
  name: string;
  unit_type: string;
  is_active: boolean;
}

interface Product {
  id: string;
  product_code: string;
  name: string;
  description: string;
  product_type: string;
  standard_cost: number | string; // API returns string, but we convert to number
  unit_of_measure: string;
  is_linear_product?: boolean;
  purchased_length?: number;
  purchased_quantity?: number;
  is_manufactured: boolean;
  is_purchased: boolean;
  is_active: boolean;
}

interface BOMItem {
  id: string;
  parent_product: string;
  component_product: string;
  component_product_name: string;
  quantity_required: number;
  unit_of_measure: string;
  unit_of_measure_ref?: string;
  cut_length?: number;
  scrap_factor: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface BOMManagementProps {
  product: Product;
  bomItems: BOMItem[];
  onBomItemsChange: (items: BOMItem[]) => void;
  onRefresh: () => void;
}

const BOMManagement: React.FC<BOMManagementProps> = ({
  product,
  bomItems,
  onBomItemsChange,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<BOMItem | null>(null);
  const [itemToDelete, setItemToDelete] = useState<BOMItem | null>(null);
  const [availableProducts, setAvailableProducts] = useState<Product[]>([]);
  const [unitsOfMeasure, setUnitsOfMeasure] = useState<UnitOfMeasure[]>([]);

  // Form state
  const [formData, setFormData] = useState({
    component_product: '',
    quantity_required: 1,
    unit_of_measure: 'EA',
    unit_of_measure_ref: '',
    cut_length: 0,
    scrap_factor: 0,
    is_active: true
  });

  useEffect(() => {
    fetchAvailableProducts();
    fetchUnitsOfMeasure();
  }, [product.id]);

  const fetchAvailableProducts = async () => {
    try {
      const products = await apiService.getProducts();
      // Filter out the current product to prevent circular references
      const filteredProducts = products.filter((p: Product) => p.id !== product.id);
      setAvailableProducts(filteredProducts);
    } catch (err) {
      console.error('Error fetching products:', err);
    }
  };

  const fetchUnitsOfMeasure = async () => {
    try {
      const units = await apiService.getUnitsOfMeasure();
      setUnitsOfMeasure(units.filter((u: UnitOfMeasure) => u.is_active));
    } catch (err) {
      console.error('Error fetching units of measure:', err);
    }
  };

  const handleAddItem = () => {
    setEditingItem(null);
    setFormData({
      component_product: '',
      quantity_required: 1,
      unit_of_measure: 'EA',
      unit_of_measure_ref: '',
      cut_length: 0,
      scrap_factor: 0,
      is_active: true
    });
    setFormDialogOpen(true);
  };

  const handleEditItem = (item: BOMItem) => {
    setEditingItem(item);
    setFormData({
      component_product: item.component_product,
      quantity_required: item.quantity_required,
      unit_of_measure: item.unit_of_measure,
      unit_of_measure_ref: item.unit_of_measure_ref || '',
      cut_length: item.cut_length || 0,
      scrap_factor: item.scrap_factor,
      is_active: item.is_active
    });
    setFormDialogOpen(true);
  };

  const handleDeleteItem = (item: BOMItem) => {
    setItemToDelete(item);
    setDeleteDialogOpen(true);
  };

  const handleFormSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      const submitData = {
        parent_product: product.id,
        component_product: formData.component_product,
        quantity_required: formData.quantity_required,
        unit_of_measure: formData.unit_of_measure,
        unit_of_measure_ref: formData.unit_of_measure_ref || null, // Convert empty string to null
        cut_length: formData.cut_length || null, // Convert 0 to null for optional field
        scrap_factor: formData.scrap_factor,
        is_active: formData.is_active
      };

      if (editingItem) {
        await apiService.updateBomItem(editingItem.id, submitData);
      } else {
        await apiService.createBomItem(submitData);
      }

      setFormDialogOpen(false);
      onRefresh();
    } catch (err: any) {
      let errorMessage = 'Failed to save BOM item';

      if (err.response?.data) {
        if (err.response.data.non_field_errors) {
          errorMessage = err.response.data.non_field_errors[0];
        } else if (err.response.data.detail) {
          errorMessage = err.response.data.detail;
        } else if (typeof err.response.data === 'string') {
          errorMessage = err.response.data;
        } else {
          // Check for field-specific errors
          const fieldErrors = Object.entries(err.response.data)
            .map(([field, errors]: [string, any]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('; ');
          if (fieldErrors) {
            errorMessage = fieldErrors;
          }
        }
      }

      // Special handling for duplicate component error
      if (errorMessage.includes('unique') || errorMessage.includes('already exists')) {
        errorMessage = 'This component is already in the BOM. Use Edit to modify the existing entry.';
      }

      setError(errorMessage);
      console.error('Error saving BOM item:', err);
      console.error('Error response data:', err.response?.data);
      console.error('Error status:', err.response?.status);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!itemToDelete) return;

    try {
      setLoading(true);
      setError(null);
      
      await apiService.deleteBomItem(itemToDelete.id);
      setDeleteDialogOpen(false);
      setItemToDelete(null);
      onRefresh();
    } catch (err) {
      setError('Failed to delete BOM item');
      console.error('Error deleting BOM item:', err);
    } finally {
      setLoading(false);
    }
  };

  const getSelectedComponentProduct = () => {
    return availableProducts.find(p => p.id === formData.component_product);
  };

  const isSelectedProductLinear = () => {
    const selectedProduct = getSelectedComponentProduct();
    return selectedProduct?.is_linear_product || false;
  };

  const calculateEffectiveQuantity = (item: BOMItem) => {
    // For linear products, this represents the effective quantity for costing
    // For non-linear products, it's just the required quantity with waste
    return item.quantity_required * (1 + item.scrap_factor);
  };

  const calculateUnitCost = (item: BOMItem) => {
    const componentProduct = availableProducts.find(p => p.id === item.component_product);
    if (!componentProduct) return 0;

    const productCost = parseFloat(componentProduct.standard_cost.toString()) || 0;

    // For linear products with cut length, calculate cost per cut length
    if (componentProduct.is_linear_product && item.cut_length && componentProduct.purchased_length) {
      const purchasedLength = parseFloat(componentProduct.purchased_length.toString()) || 0;

      if (purchasedLength > 0) {
        // Cost per unit length: total cost ÷ total length
        const costPerUnit = productCost / purchasedLength;
        // Cost per cut length: cost per unit × cut length
        return costPerUnit * item.cut_length;
      }
    }

    // For non-linear products or when cut length is not specified, use standard cost
    return productCost;
  };

  const calculateTotalCost = () => {
    return bomItems.reduce((total, item) => {
      const unitCost = calculateUnitCost(item);
      // Use base quantity for cost calculation, not effective quantity
      return total + (unitCost * item.quantity_required);
    }, 0);
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Bill of Materials</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddItem}
            disabled={loading}
          >
            Add Component
          </Button>
          <IconButton onClick={onRefresh} title="Refresh">
            <RefreshIcon />
          </IconButton>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {bomItems.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="textSecondary">
            Total Material Cost: <strong>${calculateTotalCost().toFixed(2)}</strong>
          </Typography>
        </Box>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Component</TableCell>
              <TableCell align="right">Quantity</TableCell>
              <TableCell align="right">Cut Length</TableCell>
              <TableCell>UOM</TableCell>
              <TableCell align="right">Effective Qty</TableCell>
              <TableCell align="right">Scrap %</TableCell>
              <TableCell align="right">Cut Cost</TableCell>
              <TableCell align="right">Total Cost</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {bomItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} align="center">
                  <Typography variant="body2" color="textSecondary">
                    No BOM items defined. Click "Add Component" to get started.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              bomItems.map((item) => {
                const componentProduct = availableProducts.find(p => p.id === item.component_product);
                const effectiveQty = calculateEffectiveQuantity(item);
                const cutCost = calculateUnitCost(item);
                // Use base quantity for cost calculation, not effective quantity
                const totalCost = cutCost * item.quantity_required;
                const uomDisplay = unitsOfMeasure.find(u => u.id === item.unit_of_measure_ref)?.code || item.unit_of_measure;

                return (
                  <TableRow key={item.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {componentProduct?.product_code || 'Unknown'}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {item.component_product_name}
                          </Typography>
                        </Box>
                        {componentProduct?.is_linear_product && (
                          <Tooltip title="Linear Product">
                            <LinearIcon fontSize="small" color="primary" />
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell align="right">{item.quantity_required}</TableCell>
                    <TableCell align="right">
                      {item.cut_length && item.cut_length > 0 ? (
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {item.cut_length}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {componentProduct?.unit_of_measure || 'mm'}
                          </Typography>
                        </Box>
                      ) : (
                        <Typography variant="body2" color="textSecondary">
                          Not specified
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>{uomDisplay}</TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        {effectiveQty.toFixed(2)}
                        {componentProduct?.is_linear_product && item.cut_length && (
                          <Typography variant="caption" color="textSecondary" display="block">
                            (incl. {(item.scrap_factor * 100).toFixed(1)}% waste)
                          </Typography>
                        )}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">{(item.scrap_factor * 100).toFixed(1)}%</TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        ${cutCost.toFixed(4)}
                        {componentProduct?.is_linear_product && item.cut_length && (
                          <Typography variant="caption" color="textSecondary" display="block">
                            (per {item.cut_length}{componentProduct.unit_of_measure || 'mm'} cut)
                          </Typography>
                        )}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">${totalCost.toFixed(2)}</TableCell>
                    <TableCell>
                      <Chip
                        label={item.is_active ? 'Active' : 'Inactive'}
                        color={item.is_active ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="Edit">
                        <IconButton
                          size="small"
                          onClick={() => handleEditItem(item)}
                          disabled={loading}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteItem(item)}
                          disabled={loading}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add/Edit BOM Item Dialog */}
      <Dialog open={formDialogOpen} onClose={() => setFormDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingItem ? 'Edit BOM Item' : 'Add BOM Item'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Component Product</InputLabel>
              <Select
                value={formData.component_product}
                onChange={(e) => setFormData({ ...formData, component_product: e.target.value })}
                label="Component Product"
              >
                {availableProducts.map((product) => (
                  <MenuItem key={product.id} value={product.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <span>{product.product_code} - {product.name}</span>
                      {product.is_linear_product && (
                        <Chip label="Linear" size="small" color="primary" />
                      )}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                label="Quantity Required"
                type="number"
                value={formData.quantity_required}
                onChange={(e) => setFormData({ ...formData, quantity_required: parseFloat(e.target.value) || 0 })}
                inputProps={{ min: 0, step: 0.0001 }}
                sx={{ flex: 1 }}
                helperText="Number of pieces/units needed"
              />

              <TextField
                label="Cut Length"
                type="number"
                value={formData.cut_length}
                onChange={(e) => setFormData({ ...formData, cut_length: parseFloat(e.target.value) || 0 })}
                inputProps={{ min: 0, step: 0.001 }}
                sx={{ flex: 1 }}
                helperText={`Length per piece (${getSelectedComponentProduct()?.unit_of_measure || 'mm/units'})`}
                placeholder="e.g., 700 for 700mm legs"
              />
            </Box>

            <FormControl fullWidth>
              <InputLabel>Unit of Measure</InputLabel>
              <Select
                value={formData.unit_of_measure_ref}
                onChange={(e) => setFormData({ ...formData, unit_of_measure_ref: e.target.value })}
                label="Unit of Measure"
              >
                <MenuItem value="">
                  <em>Use legacy UOM ({formData.unit_of_measure})</em>
                </MenuItem>
                {unitsOfMeasure.map((uom) => (
                  <MenuItem key={uom.id} value={uom.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <span>{uom.code} - {uom.name}</span>
                      <Chip label={uom.unit_type.toUpperCase()} size="small" variant="outlined" />
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Cut Length Examples */}
            {formData.cut_length > 0 && (
              <Alert severity="info" sx={{ mt: 1 }}>
                <Typography variant="body2">
                  <strong>Cut Length Specification:</strong><br />
                  {formData.quantity_required} pieces × {formData.cut_length} {getSelectedComponentProduct()?.unit_of_measure || 'mm'} each
                  {isSelectedProductLinear() && getSelectedComponentProduct()?.purchased_length && (
                    <>
                      <br />
                      <strong>Cost Calculation:</strong><br />
                      {(() => {
                        const product = getSelectedComponentProduct();
                        if (product?.purchased_length && formData.cut_length > 0) {
                          const purchasedLength = parseFloat(product.purchased_length.toString()) || 0;
                          const productCost = parseFloat(product.standard_cost.toString()) || 0;

                          if (purchasedLength > 0 && productCost > 0) {
                            const costPerUnit = productCost / purchasedLength;
                            const cutCost = costPerUnit * formData.cut_length;
                            const effectiveQty = formData.quantity_required * (1 + formData.scrap_factor);
                            // Use base quantity for cost calculation, not effective quantity
                            const totalCost = cutCost * formData.quantity_required;

                            return (
                              <>
                                Bar cost: ${productCost.toFixed(2)} for {purchasedLength}{product.unit_of_measure}<br />
                                Cost per {product.unit_of_measure}: ${costPerUnit.toFixed(6)}<br />
                                Cost per {formData.cut_length}{product.unit_of_measure} cut: ${cutCost.toFixed(4)}<br />
                                Effective quantity (with waste): {effectiveQty.toFixed(2)}<br />
                                Total cost: ${totalCost.toFixed(2)}
                              </>
                            );
                          }
                        }
                        return 'Calculation not available';
                      })()}
                    </>
                  )}
                </Typography>
              </Alert>
            )}

            {!formData.cut_length && (
              <Alert severity="success" sx={{ mt: 1 }}>
                <Typography variant="body2">
                  <strong>💡 Cut Length Examples:</strong><br />
                  • Table legs: 700 (for 700mm legs)<br />
                  • Steel bars: 1200 (for 1.2m pieces)<br />
                  • Pipes: 500 (for 500mm sections)<br />
                  • Leave blank for standard components
                </Typography>
              </Alert>
            )}

            <TextField
              label="Scrap Factor (%)"
              type="number"
              value={formData.scrap_factor * 100}
              onChange={(e) => setFormData({ ...formData, scrap_factor: (parseFloat(e.target.value) || 0) / 100 })}
              inputProps={{ min: 0, max: 100, step: 0.1 }}
              fullWidth
              helperText="Percentage of additional material needed due to scrap/waste"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFormDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleFormSubmit}
            variant="contained"
            disabled={loading || !formData.component_product}
          >
            {loading ? <CircularProgress size={20} /> : (editingItem ? 'Update' : 'Add')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this BOM item?
            {itemToDelete && (
              <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
                <Typography variant="body2">
                  <strong>Component:</strong> {itemToDelete.component_product_name}
                </Typography>
                <Typography variant="body2">
                  <strong>Quantity:</strong> {itemToDelete.quantity_required} {itemToDelete.unit_of_measure}
                </Typography>
                {itemToDelete.cut_length && itemToDelete.cut_length > 0 && (
                  <Typography variant="body2">
                    <strong>Cut Length:</strong> {itemToDelete.cut_length} mm
                  </Typography>
                )}
              </Box>
            )}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BOMManagement;
