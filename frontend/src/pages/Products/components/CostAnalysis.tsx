import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Calculate as CalculateIcon
} from '@mui/icons-material';
import { apiService } from '../../../services/api';

interface Product {
  id: string;
  product_code: string;
  name: string;
  description: string;
  product_type: string;
  standard_cost: number;
  unit_of_measure: string;
  is_manufactured: boolean;
  is_purchased: boolean;
  is_active: boolean;
}

interface CostBreakdown {
  product: Product;
  quantity: number;
  material_cost: number;
  labor_cost: number;
  total_cost: number;
}

interface BOMExplosion {
  product: Product;
  quantity: number;
  explosion: Array<{
    product_code: string;
    product_name: string;
    quantity_required: number;
    unit_cost: number;
    total_cost: number;
    level: number;
  }>;
}

interface CostAnalysisProps {
  product: Product;
  costData: CostBreakdown | null;
  onRefresh: () => void;
}

const CostAnalysis: React.FC<CostAnalysisProps> = ({
  product,
  costData,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [currentCostData, setCurrentCostData] = useState<CostBreakdown | null>(costData);
  const [bomExplosion, setBomExplosion] = useState<BOMExplosion | null>(null);

  useEffect(() => {
    setCurrentCostData(costData);
  }, [costData]);

  const calculateCosts = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch cost breakdown
      const costResponse = await apiService.getProductCostBreakdown(product.id, quantity);
      setCurrentCostData(costResponse);

      // Fetch BOM explosion for detailed material breakdown
      try {
        const bomResponse = await apiService.getProductBomExplosion(product.id, quantity);
        setBomExplosion(bomResponse);
      } catch (bomErr) {
        // BOM explosion might not be available if no BOM items exist
        setBomExplosion(null);
      }
    } catch (err) {
      setError('Failed to calculate costs');
      console.error('Error calculating costs:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleQuantityChange = (newQuantity: number) => {
    setQuantity(newQuantity);
    if (newQuantity > 0) {
      calculateCosts();
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getCostPerUnit = () => {
    if (!currentCostData || quantity === 0) return 0;
    return currentCostData.total_cost / quantity;
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Cost Analysis</Typography>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <TextField
            label="Quantity"
            type="number"
            value={quantity}
            onChange={(e) => handleQuantityChange(parseFloat(e.target.value) || 1)}
            inputProps={{ min: 1, step: 1 }}
            size="small"
            sx={{ width: 120 }}
          />
          <Button
            variant="contained"
            startIcon={<CalculateIcon />}
            onClick={calculateCosts}
            disabled={loading}
          >
            Calculate
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={onRefresh}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading && (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
        </Box>
      )}

      {!loading && currentCostData && (
        <>
          {/* Cost Summary Cards */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Material Cost
                  </Typography>
                  <Typography variant="h5" color="primary">
                    {formatCurrency(currentCostData.material_cost)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {formatCurrency(currentCostData.material_cost / quantity)} per unit
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Labor Cost
                  </Typography>
                  <Typography variant="h5" color="secondary">
                    {formatCurrency(currentCostData.labor_cost)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {formatCurrency(currentCostData.labor_cost / quantity)} per unit
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Total Cost
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {formatCurrency(currentCostData.total_cost)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {formatCurrency(getCostPerUnit())} per unit
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Standard Cost
                  </Typography>
                  <Typography variant="h5">
                    {formatCurrency(product.standard_cost * quantity)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {formatCurrency(product.standard_cost)} per unit
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Cost Variance Analysis */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Cost Variance Analysis
              </Typography>
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Box>
                    <Typography variant="body2" color="textSecondary">
                      Calculated vs Standard
                    </Typography>
                    <Typography variant="h6">
                      {formatCurrency(currentCostData.total_cost - (product.standard_cost * quantity))}
                    </Typography>
                    <Chip
                      label={currentCostData.total_cost > (product.standard_cost * quantity) ? 'Over Budget' : 'Under Budget'}
                      color={currentCostData.total_cost > (product.standard_cost * quantity) ? 'error' : 'success'}
                      size="small"
                    />
                  </Box>
                </Grid>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Box>
                    <Typography variant="body2" color="textSecondary">
                      Material vs Labor Ratio
                    </Typography>
                    <Typography variant="body1">
                      {currentCostData.total_cost > 0 ?
                        `${((currentCostData.material_cost / currentCostData.total_cost) * 100).toFixed(1)}% Material` :
                        'N/A'
                      }
                    </Typography>
                    <Typography variant="body1">
                      {currentCostData.total_cost > 0 ?
                        `${((currentCostData.labor_cost / currentCostData.total_cost) * 100).toFixed(1)}% Labor` :
                        'N/A'
                      }
                    </Typography>
                  </Box>
                </Grid>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Box>
                    <Typography variant="body2" color="textSecondary">
                      Quantity
                    </Typography>
                    <Typography variant="h6">
                      {quantity} {product.unit_of_measure}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Material Breakdown */}
          {bomExplosion && bomExplosion.explosion.length > 0 && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Material Breakdown
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Component</TableCell>
                        <TableCell align="right">Quantity Required</TableCell>
                        <TableCell align="right">Unit Cost</TableCell>
                        <TableCell align="right">Total Cost</TableCell>
                        <TableCell>Level</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {bomExplosion.explosion.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Box sx={{ pl: item.level * 2 }}>
                              <Typography variant="body2" fontWeight="medium">
                                {item.product_code}
                              </Typography>
                              <Typography variant="caption" color="textSecondary">
                                {item.product_name}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell align="right">{item.quantity_required.toFixed(4)}</TableCell>
                          <TableCell align="right">{formatCurrency(item.unit_cost)}</TableCell>
                          <TableCell align="right">{formatCurrency(item.total_cost)}</TableCell>
                          <TableCell>
                            <Chip
                              label={`Level ${item.level}`}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {!loading && !currentCostData && (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            No cost data available. Click "Calculate" to generate cost analysis.
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default CostAnalysis;
