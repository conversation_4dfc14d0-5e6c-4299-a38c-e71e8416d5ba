import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { apiService } from '../../services/api';
import ProductDetailDialog from './components/ProductDetailDialog';
import ProductFormDialog from './components/ProductFormDialog';

interface Product {
  id: string;
  product_code: string;
  name: string;
  description: string;
  product_type: string;
  standard_cost: number | string; // API returns string, but we convert to number
  unit_of_measure: string;
  unit_of_measure_ref?: string | null;
  is_linear_product: boolean;
  purchased_length?: number | null;
  purchased_quantity: number;
  is_manufactured: boolean;
  is_purchased: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`products-tabpanel-${index}`}
      aria-labelledby={`products-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const Products: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getProducts();
      const productsData = response.results || response;
      
      // Ensure numeric fields are properly converted
      const normalizedProducts = productsData.map((product: any) => ({
        ...product,
        standard_cost: parseFloat(product.standard_cost) || 0,
        is_linear_product: product.is_linear_product || false,
        purchased_length: product.purchased_length ? parseFloat(product.purchased_length) : null,
        purchased_quantity: parseFloat(product.purchased_quantity) || 1
      }));
      
      setProducts(normalizedProducts);
    } catch (err) {
      setError('Failed to fetch products');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleFilterChange = (event: any) => {
    setFilterType(event.target.value);
  };

  const handleViewProduct = (product: Product) => {
    setSelectedProduct(product);
    setDetailDialogOpen(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setFormDialogOpen(true);
  };

  const handleDeleteProduct = (product: Product) => {
    setProductToDelete(product);
    setDeleteDialogOpen(true);
  };

  const handleAddProduct = () => {
    setEditingProduct(null);
    setFormDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!productToDelete) return;

    try {
      await apiService.deleteProduct(productToDelete.id);
      setProducts(products.filter(p => p.id !== productToDelete.id));
      setDeleteDialogOpen(false);
      setProductToDelete(null);
    } catch (err) {
      setError('Failed to delete product');
      console.error('Error deleting product:', err);
    }
  };

  const handleFormSubmit = async (productData: any) => {
    try {
      if (editingProduct) {
        const updatedProduct = await apiService.updateProduct(editingProduct.id, productData);
        // Normalize the updated product data
        const normalizedProduct = {
          ...updatedProduct,
          standard_cost: parseFloat(updatedProduct.standard_cost.toString()) || 0,
          is_linear_product: updatedProduct.is_linear_product || false,
          purchased_length: updatedProduct.purchased_length ? parseFloat(updatedProduct.purchased_length.toString()) : null,
          purchased_quantity: parseFloat(updatedProduct.purchased_quantity.toString()) || 1
        };
        setProducts(products.map(p => p.id === editingProduct.id ? normalizedProduct : p));
      } else {
        const newProduct = await apiService.createProduct(productData);
        // Normalize the new product data
        const normalizedProduct = {
          ...newProduct,
          standard_cost: parseFloat(newProduct.standard_cost.toString()) || 0,
          is_linear_product: newProduct.is_linear_product || false,
          purchased_length: newProduct.purchased_length ? parseFloat(newProduct.purchased_length.toString()) : null,
          purchased_quantity: parseFloat(newProduct.purchased_quantity.toString()) || 1
        };
        setProducts([...products, normalizedProduct]);
      }
      setFormDialogOpen(false);
      setEditingProduct(null);
    } catch (err) {
      setError('Failed to save product');
      console.error('Error saving product:', err);
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.product_code.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterType === 'all' || 
                         (filterType === 'manufactured' && product.is_manufactured) ||
                         (filterType === 'purchased' && product.is_purchased) ||
                         (filterType === 'active' && product.is_active) ||
                         (filterType === 'inactive' && !product.is_active) ||
                         product.product_type === filterType;
    
    return matchesSearch && matchesFilter;
  });

  const getProductTypeColor = (type: string) => {
    switch (type) {
      case 'manufactured': return 'primary';
      case 'purchased': return 'secondary';
      case 'raw_material': return 'warning';
      case 'finished_good': return 'success';
      default: return 'default';
    }
  };

  const productStats = {
    total: products.length,
    manufactured: products.filter(p => p.is_manufactured).length,
    purchased: products.filter(p => p.is_purchased).length,
    active: products.filter(p => p.is_active).length,
    inactive: products.filter(p => !p.is_active).length
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Products Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchProducts}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddProduct}
          >
            Add Product
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="products tabs">
          <Tab label="Products List" />
          <Tab label="Analytics" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        {/* Search and Filter Controls */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2, alignItems: { md: 'center' } }}>
            <Box sx={{ flex: { md: 2 } }}>
              <TextField
                fullWidth
                placeholder="Search products..."
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            <Box sx={{ flex: { md: 1 } }}>
              <FormControl fullWidth>
                <InputLabel>Filter by Type</InputLabel>
                <Select
                  value={filterType}
                  label="Filter by Type"
                  onChange={handleFilterChange}
                >
                  <MenuItem value="all">All Products</MenuItem>
                  <MenuItem value="manufactured">Manufactured</MenuItem>
                  <MenuItem value="purchased">Purchased</MenuItem>
                  <MenuItem value="raw_material">Raw Materials</MenuItem>
                  <MenuItem value="finished_good">Finished Goods</MenuItem>
                  <MenuItem value="active">Active Only</MenuItem>
                  <MenuItem value="inactive">Inactive Only</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Box sx={{ flex: { md: 1 } }}>
              <Typography variant="body2" color="textSecondary">
                Showing {filteredProducts.length} of {products.length} products
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Products Table */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Product Code</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Standard Cost</TableCell>
                <TableCell>UOM</TableCell>
                <TableCell>Manufacturing</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredProducts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    <Typography variant="body1" color="textSecondary" sx={{ py: 4 }}>
                      {products.length === 0 
                        ? "No products found. Add your first product to get started with manufacturing."
                        : "No products match your search criteria."
                      }
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredProducts.map((product) => (
                  <TableRow key={product.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {product.product_code}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {product.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={product.product_type.replace('_', ' ').toUpperCase()}
                        color={getProductTypeColor(product.product_type) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        ${(parseFloat(product.standard_cost.toString()) || 0).toFixed(2)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {product.unit_of_measure}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        {product.is_manufactured && (
                          <Chip label="MFG" color="primary" size="small" />
                        )}
                        {product.is_purchased && (
                          <Chip label="PUR" color="secondary" size="small" />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={product.is_active ? 'Active' : 'Inactive'}
                        color={product.is_active ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewProduct(product)}
                          title="View Details"
                        >
                          <ViewIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleEditProduct(product)}
                          title="Edit Product"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteProduct(product)}
                          title="Delete Product"
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {/* Analytics Dashboard */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 3, flexWrap: 'wrap' }}>
          <Box sx={{ flex: { sm: '1 1 200px' } }}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Products
                </Typography>
                <Typography variant="h4">
                  {productStats.total}
                </Typography>
              </CardContent>
            </Card>
          </Box>
          <Box sx={{ flex: { sm: '1 1 200px' } }}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Manufactured
                </Typography>
                <Typography variant="h4" color="primary">
                  {productStats.manufactured}
                </Typography>
              </CardContent>
            </Card>
          </Box>
          <Box sx={{ flex: { sm: '1 1 200px' } }}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Purchased
                </Typography>
                <Typography variant="h4" color="secondary">
                  {productStats.purchased}
                </Typography>
              </CardContent>
            </Card>
          </Box>
          <Box sx={{ flex: { sm: '1 1 200px' } }}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Active Products
                </Typography>
                <Typography variant="h4" color="success.main">
                  {productStats.active}
                </Typography>
              </CardContent>
            </Card>
          </Box>
        </Box>
      </TabPanel>

      {/* Product Detail Dialog */}
      {selectedProduct && (
        <ProductDetailDialog
          open={detailDialogOpen}
          onClose={() => setDetailDialogOpen(false)}
          product={selectedProduct}
        />
      )}

      {/* Product Form Dialog */}
      <ProductFormDialog
        open={formDialogOpen}
        onClose={() => setFormDialogOpen(false)}
        onSubmit={handleFormSubmit}
        product={editingProduct}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the product "{productToDelete?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Products;