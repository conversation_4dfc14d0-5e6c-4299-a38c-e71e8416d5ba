import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';

const WorkOrders: React.FC = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Work Orders
        </Typography>
        <Button variant="contained" color="primary">
          Create Work Order
        </Button>
      </Box>
      
      <Paper sx={{ p: 3 }}>
        <Typography variant="body1" color="textSecondary">
          No work orders found. Create your first work order or import from Xero invoices.
        </Typography>
      </Paper>
    </Box>
  );
};

export default WorkOrders;