const API_BASE_URL = 'http://localhost:8000/api';

class ApiService {
  private async request<T = any>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Products API
  async getProducts() {
    const response = await this.request('/core/products/');
    return response.results || response; // Handle paginated response
  }

  async getProduct(id: string) {
    return this.request(`/core/products/${id}/`);
  }

  async createProduct(data: any) {
    return this.request('/core/products/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateProduct(id: string, data: any) {
    return this.request(`/core/products/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteProduct(id: string) {
    return this.request(`/core/products/${id}/`, {
      method: 'DELETE',
    });
  }

  async getProductBomExplosion(id: string, quantity: number = 1) {
    return this.request(`/core/products/${id}/bom_explosion/?quantity=${quantity}`);
  }

  async getProductCostBreakdown(id: string, quantity: number = 1) {
    return this.request(`/core/products/${id}/cost_breakdown/?quantity=${quantity}`);
  }

  // Work Orders API
  async getWorkOrders() {
    return this.request('/manufacturing/work-orders/');
  }

  async getWorkOrder(id: string) {
    return this.request(`/manufacturing/work-orders/${id}/`);
  }

  async createWorkOrder(data: any) {
    return this.request('/manufacturing/work-orders/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async createWorkOrderFromXeroInvoice(invoiceId: string) {
    return this.request('/manufacturing/work-orders/create_from_xero_invoice/', {
      method: 'POST',
      body: JSON.stringify({ invoice_id: invoiceId }),
    });
  }

  async calculateWorkOrderCosts(id: string) {
    return this.request(`/manufacturing/work-orders/${id}/calculate_costs/`);
  }

  async getWorkOrderMaterialRequirements(id: string) {
    return this.request(`/manufacturing/work-orders/${id}/material_requirements/`);
  }

  async startWorkOrderProduction(id: string) {
    return this.request(`/manufacturing/work-orders/${id}/start_production/`, {
      method: 'POST',
    });
  }

  async completeWorkOrderProduction(id: string) {
    return this.request(`/manufacturing/work-orders/${id}/complete_production/`, {
      method: 'POST',
    });
  }

  // Inventory API
  async getInventoryLevels() {
    return this.request('/inventory/inventory-levels/');
  }

  async getInventoryTransactions() {
    return this.request('/inventory/inventory-transactions/');
  }

  async createInventoryReceipt(data: any) {
    return this.request('/inventory/inventory-transactions/create_receipt/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async createInventoryIssue(data: any) {
    return this.request('/inventory/inventory-transactions/create_issue/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getLowStockItems(threshold: number = 10) {
    return this.request(`/inventory/inventory-levels/low_stock_items/?threshold=${threshold}`);
  }

  async getOutOfStockItems() {
    return this.request('/inventory/inventory-levels/out_of_stock_items/');
  }

  async getStockAlerts() {
    return this.request('/inventory/stock-alerts/');
  }

  async acknowledgeStockAlert(id: string) {
    return this.request(`/inventory/stock-alerts/${id}/acknowledge/`, {
      method: 'POST',
    });
  }

  // Xero Integration API
  async getXeroInvoices() {
    return this.request('/xero/invoices/');
  }

  async getXeroInvoice(id: string) {
    return this.request(`/xero/invoices/${id}/`);
  }

  // BOM API
  async getBomItems() {
    const response = await this.request('/core/bom-items/');
    return response.results || response; // Handle paginated response
  }

  async createBomItem(data: any) {
    return this.request('/core/bom-items/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateBomItem(id: string, data: any) {
    return this.request(`/core/bom-items/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteBomItem(id: string) {
    return this.request(`/core/bom-items/${id}/`, {
      method: 'DELETE',
    });
  }

  // Work Centers API
  async getWorkCenters() {
    const response = await this.request('/core/work-centers/');
    return response.results || response; // Handle paginated response
  }

  async createWorkCenter(data: any) {
    return this.request('/core/work-centers/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Routing Steps API
  async getRoutingSteps() {
    const response = await this.request('/core/routing-steps/');
    return response.results || response; // Handle paginated response
  }

  async createRoutingStep(data: any) {
    return this.request('/core/routing-steps/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateRoutingStep(id: string, data: any) {
    return this.request(`/core/routing-steps/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteRoutingStep(id: string) {
    return this.request(`/core/routing-steps/${id}/`, {
      method: 'DELETE',
    });
  }

  // Cost Calculations API
  async getCostCalculations() {
    return this.request('/manufacturing/cost-calculations/');
  }

  async calculateCosts(workOrderId: string) {
    return this.request('/manufacturing/cost-calculations/calculate_costs/', {
      method: 'POST',
      body: JSON.stringify({ work_order_id: workOrderId }),
    });
  }

  // Units of Measure API
  async getUnitsOfMeasure() {
    const response = await this.request('/core/units-of-measure/');
    return response.results || response; // Handle paginated response
  }

  async createUnitOfMeasure(data: any) {
    return this.request('/core/units-of-measure/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateUnitOfMeasure(id: string, data: any) {
    return this.request(`/core/units-of-measure/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteUnitOfMeasure(id: string) {
    return this.request(`/core/units-of-measure/${id}/`, {
      method: 'DELETE',
    });
  }
}

export const apiService = new ApiService();
export default apiService;