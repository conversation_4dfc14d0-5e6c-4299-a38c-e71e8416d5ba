import { configureStore } from '@reduxjs/toolkit';
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Define the API base URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// Create the main API slice
export const api = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Add authentication headers if needed
      const token = localStorage.getItem('authToken');
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: [
    'Product',
    'WorkCenter',
    'BOMItem',
    'RoutingStep',
    'WorkOrder',
    'WorkOrderItem',
    'ProductionRecord',
    'CostCalculation',
    'InventoryTransaction',
    'InventoryLevel',
    'StockAlert',
    'XeroInvoice',
    'XeroInvoiceLineItem',
    'XeroSyncLog',
    'XeroConnection',
  ],
  endpoints: () => ({}),
});

// Configure the store
export const store = configureStore({
  reducer: {
    [api.reducerPath]: api.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(api.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;