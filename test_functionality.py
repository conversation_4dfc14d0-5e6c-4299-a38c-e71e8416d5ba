#!/usr/bin/env python
"""
Test script to demonstrate the core manufacturing ERP functionality
"""
import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from core.models import Product
from manufacturing.models import WorkOrder
from xero_integration.models import XeroInvoice
from inventory.models import InventoryLevel

def test_bom_explosion():
    """Test BOM explosion functionality"""
    print("=== Testing BOM Explosion ===")
    
    # Get a finished good product
    fg_product = Product.objects.filter(product_code='FG001').first()
    if fg_product:
        print(f"Testing BOM explosion for: {fg_product.product_code} - {fg_product.name}")
        
        # Test BOM explosion for quantity 2
        explosion = fg_product.explode_bom(quantity=2)
        
        print(f"BOM Explosion for {fg_product.product_code} (Qty: 2):")
        for component_code, data in explosion.items():
            print(f"  - {component_code}: {data['quantity']} units (Level {data['level']})")
            print(f"    Cost: ${data['product'].standard_cost} each = ${float(data['quantity'] * data['product'].standard_cost):.2f} total")
        
        # Test cost calculation
        material_cost = fg_product.get_bom_cost(quantity=2)
        labor_cost = fg_product.get_labor_cost(quantity=2)
        
        print(f"\nCost Breakdown for {fg_product.product_code} (Qty: 2):")
        print(f"  Material Cost: ${material_cost:.2f}")
        print(f"  Labor Cost: ${labor_cost:.2f}")
        print(f"  Total Cost: ${material_cost + labor_cost:.2f}")
    else:
        print("No finished good product found")

def test_work_order_creation():
    """Test work order creation from Xero invoice"""
    print("\n=== Testing Work Order Creation ===")
    
    # Get the sample Xero invoice
    xero_invoice = XeroInvoice.objects.filter(invoice_number='INV-001').first()
    if xero_invoice:
        print(f"Found Xero Invoice: {xero_invoice.invoice_number} - {xero_invoice.contact_name}")
        print(f"Total Amount: ${xero_invoice.total_amount}")
        
        # Create work order from invoice
        work_order = WorkOrder.create_from_xero_invoice(xero_invoice)
        print(f"Created Work Order: {work_order.work_order_number}")
        print(f"Status: {work_order.status}")
        
        # Show work order items
        print("Work Order Items:")
        for item in work_order.items.all():
            print(f"  - {item.product.product_code}: {item.quantity_required} units")
        
        # Calculate costs
        cost_calc = work_order.calculate_costs()
        print(f"\nWork Order Cost Calculation:")
        print(f"  Material Cost: ${cost_calc.material_cost:.2f}")
        print(f"  Labor Cost: ${cost_calc.labor_cost:.2f}")
        print(f"  Overhead Cost: ${cost_calc.overhead_cost:.2f}")
        print(f"  Total Cost: ${cost_calc.total_cost:.2f}")
        print(f"  Margin: {cost_calc.margin_percentage}%")
        print(f"  Selling Price: ${cost_calc.selling_price:.2f}")
        
        # Generate material requirements
        requirements = work_order.generate_material_requirements()
        print(f"\nMaterial Requirements:")
        for component_code, data in requirements.items():
            print(f"  - {component_code}: {data['quantity']} units")
            print(f"    Total Cost: ${float(data['quantity'] * data['product'].standard_cost):.2f}")
        
        return work_order
    else:
        print("No Xero invoice found")
        return None

def test_inventory_tracking():
    """Test inventory tracking functionality"""
    print("\n=== Testing Inventory Tracking ===")
    
    # Show current inventory levels
    inventory_levels = InventoryLevel.objects.all()
    print("Current Inventory Levels:")
    for level in inventory_levels:
        print(f"  - {level.product.product_code}: {level.quantity_on_hand} on hand, {level.quantity_available} available")
        print(f"    Average Cost: ${level.average_cost:.2f}")

def main():
    """Run all tests"""
    print("Manufacturing ERP System - Core Functionality Test")
    print("=" * 50)
    
    # Test BOM explosion
    test_bom_explosion()
    
    # Test work order creation
    work_order = test_work_order_creation()
    
    # Test inventory tracking
    test_inventory_tracking()
    
    print("\n" + "=" * 50)
    print("Core functionality test completed successfully!")
    print("\nKey Features Demonstrated:")
    print("✓ BOM Explosion - Calculate all required materials")
    print("✓ Cost Calculation - Material + Labor + Overhead costs")
    print("✓ Work Order Generation - From Xero invoices")
    print("✓ Inventory Tracking - Real-time stock levels")
    print("✓ Material Requirements Planning - For work orders")

if __name__ == '__main__':
    main()